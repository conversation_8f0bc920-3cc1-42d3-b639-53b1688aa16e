// Why Choose Us Section
.whyChooseSection {
  position: relative;
  padding: 8rem 0;
  background: radial-gradient(
      ellipse 800px 400px at center top,
      rgba(0, 168, 204, 0.02) 0%,
      rgba(0, 168, 204, 0.005) 40%,
      transparent 70%
    ),
    radial-gradient(
      ellipse 600px 300px at center bottom,
      rgba(0, 119, 190, 0.015) 0%,
      rgba(0, 119, 190, 0.003) 40%,
      transparent 70%
    ),
    linear-gradient(
      135deg,
      #010204 0%,
      #020304 25%,
      #040507 50%,
      #020304 75%,
      #020306 100%
    );
  overflow: hidden;
}

// Section Header
.sectionHeader {
  text-align: center;
  margin-bottom: 3rem;
}

.imgContainer{
  border: 3px solid red !important;
}

.divInImgContainer{
  border: 3px solid red !important;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #00a8cc 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
}

// Cards Grid
.cardsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin: 3rem 5rem;
}

// Individual Card
.card {
  position: relative;
  padding: 3rem 2rem;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.03);
  border-color: rgba(0, 168, 204, 0.2);
  backdrop-filter: blur(15px);
  transition: all 0.4s ease;
  overflow: hidden;
  cursor: pointer;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 168, 204, 0.4) 50%,
      transparent 100%
    );
    opacity: 1;
    transition: opacity 0.4s ease;
  }

  &:hover {
    transform: translateY(-10px);

    .cardGlow {
      opacity: 1;
      transform: scale(1.1);
    }

    .cardIconContainer {
      transform: scale(1.1);
      border-color: rgba(0, 168, 204, 0.4);
      box-shadow: 0 0 30px rgba(0, 168, 204, 0.2);

      &::before {
        opacity: 1;
      }

      .cardIcon {
        transform: scale(1.1) rotate(5deg);
        color: #ffffff;
        filter: drop-shadow(0 0 15px rgba(0, 168, 204, 0.5));
      }
    }

    .icon {
      transform: scale(1.2) rotate(10deg);
    }
  }
}

// Card Icon Container
.cardIconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 65px;
  height: 65px;
  margin: 0 auto 2rem auto;
  background: linear-gradient(135deg, rgba(0, 168, 204, 0.1), rgba(0, 119, 190, 0.05));
  border: 2px solid rgba(0, 168, 204, 0.2);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0, 168, 204, 0.05), transparent);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .cardIcon {
    width: 40px;
    height: 40px;
    color: #00a8cc;
    transition: all 0.4s ease;
    filter: drop-shadow(0 0 10px rgba(0, 168, 204, 0.3));
    z-index: 1;
    flex-shrink: 0;
  }
}

// Card Content
.cardContent {
  text-align: center;
}

.cardTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
}

.cardDescription {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

// Card Glow Effect
.cardGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(0, 168, 204, 0.04) 0%,
    transparent 70%
  );
  border-radius: 50%;
  opacity: 0;
  transition: all 0.4s ease;
  pointer-events: none;
  z-index: -1;
}

// Responsive Design
@media (max-width: 1200px) {
  .whyChooseSection {
    padding: 6rem 0;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .cardsGrid {
    gap: 1.5rem;
  }

  .trustCard {
    padding: 2rem 1.5rem;
  }

  .cardIconContainer {
    width: 70px;
    height: 70px;
    margin-bottom: 1.5rem;

    .cardIcon {
      width: 42px;
      height: 42px;
    }
  }

  .icon {
    font-size: 2.5rem;
  }
}

@media (max-width: 968px) {
  .whyChooseSection {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .sectionHeader {
    margin-bottom: 4rem;
  }

  .sectionTitle {
    font-size: 2.25rem;
  }

  .sectionSubtitle {
    font-size: 1.1rem;
  }

  .cardsGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .cardTitle {
    font-size: 1.3rem;
  }

  .cardDescription {
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .whyChooseSection {
    padding: 4rem 0;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }

  .cardsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    max-width: 400px;
  }

  .trustCard {
    padding: 2rem 1.5rem;
  }

  .cardIconContainer {
    width: 65px;
    height: 65px;

    .cardIcon {
      width: 38px;
      height: 38px;
    }
  }

  .icon {
    font-size: 2rem;
  }

  .cardTitle {
    font-size: 1.2rem;
  }

  .cardDescription {
    font-size: 0.9rem;
  }

  .highlightBadge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

@media (max-width: 480px) {
  .whyChooseSection {
    padding: 3rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .sectionTitle {
    font-size: 1.75rem;
  }

  .trustCard {
    padding: 1.5rem 1rem;
  }

  .cardIconContainer {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;

    .cardIcon {
      width: 32px;
      height: 32px;
    }
  }

  .icon {
    font-size: 1.75rem;
  }

  .cardTitle {
    font-size: 1.1rem;
  }

  .cardDescription {
    font-size: 0.85rem;
  }

  .highlightBadge {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
  }
}
