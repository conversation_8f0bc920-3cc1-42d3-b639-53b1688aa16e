.servicesContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background: radial-gradient(
    circle at center,
    rgba(0, 69, 84, 0.5) -10%,
    rgba(5, 5, 5) 40%
  );
  z-index: 9;

  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: url("/assets/bg/bg1.jpg") center/cover no-repeat fixed
      border-box;
    filter: blur(3px);
    z-index: -9;
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;

    span {
      font-size: 2.5rem;
      font-weight: 600;
    }
  }

  h4 {
    font-size: 1.25rem;
    text-align: center;
  }

  p {
    font-size: 1rem;
  }
}
