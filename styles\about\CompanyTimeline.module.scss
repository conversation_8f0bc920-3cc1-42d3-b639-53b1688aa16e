// Our Journey Starts Here Section
.journeySection {
  position: relative;
  padding: 8rem 0;
  background: radial-gradient(
      ellipse 600px 400px at top right,
      rgba(0, 168, 204, 0.03) 0%,
      rgba(0, 168, 204, 0.008) 40%,
      transparent 70%
    ),
    radial-gradient(
      ellipse 500px 300px at bottom left,
      rgba(0, 119, 190, 0.025) 0%,
      rgba(0, 119, 190, 0.006) 40%,
      transparent 70%
    ),
    linear-gradient(
      135deg,
      #010204 0%,
      #020304 25%,
      #040507 50%,
      #020304 75%,
      #020306 100%
    );
  overflow: hidden;

  h3 {
    font-size: 1.75rem;
  }
  p {
    text-align: start;
    font-size: 1.25rem;
  }
  div{
    font-size: 1.05rem;
  }
}

// Section Header
.sectionHeader {
  text-align: center;
  margin-bottom: 1rem;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  margin: 1rem auto;
  text-align: center;
  width: 100%;
}

.sectionSubtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: center !important;
}

// // Steps Container
// .stepsContainer {
//   display: grid;
//   grid-template-columns: repeat(4, 1fr);
//   gap: 2rem;
//   max-width: 90vw;
//   margin: 0 auto;
//   position: relative;
// }

// // Individual Step
// .step {
//   position: relative;
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   text-align: center;
//   padding: 2rem 1rem;
//   background: rgba(255, 255, 255, 0.02);
//   border: 1px solid rgba(255, 255, 255, 0.06);
//   border-radius: 20px;
//   backdrop-filter: blur(15px);
//   transition: all 0.4s ease;

//   &:hover {
//     transform: translateY(-10px);
//     background: rgba(255, 255, 255, 0.04);
//     border-color: rgba(0, 168, 204, 0.3);
//     box-shadow:
//       0 25px 50px rgba(0, 0, 0, 0.3),
//       0 0 40px rgba(0, 168, 204, 0.1);
//   }
// }

// // Step Icon
// .stepIcon {
//   position: relative;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   width: 80px;
//   height: 80px;
//   margin-bottom: 2rem;
//   background: rgba(0, 168, 204, 0.1);
//   border: 2px solid rgba(0, 168, 204, 0.2);
//   border-radius: 50%;
//   transition: all 0.4s ease;
// }

// .iconContainer {
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   width: 100%;
//   height: 100%;
// }

// .icon {
//   width: 40px;
//   height: 40px;
//   color: rgba(255, 255, 255, 0.9);
//   transition: all 0.4s ease;
//   filter: drop-shadow(0 0 10px rgba(0, 168, 204, 0.3));
// }

// .iconGlow {
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%);
//   width: 100px;
//   height: 100px;
//   background: radial-gradient(circle, rgba(0, 168, 204, 0.2) 0%, transparent 70%);
//   border-radius: 50%;
//   opacity: 0;
//   transition: all 0.4s ease;
//   z-index: -1;
// }

// .step:hover .stepIcon {
//   transform: scale(1.1);
//   border-color: rgba(0, 168, 204, 0.4);
//   background: rgba(0, 168, 204, 0.15);
// }

// .step:hover .icon {
//   transform: scale(1.1);
// }

// .step:hover .iconGlow {
//   opacity: 1;
//   transform: translate(-50%, -50%) scale(1.2);
// }

// // Step Content
// .stepContent {
//   flex: 1;
// }

// .stepPhase {
//   display: inline-block;
//   background: linear-gradient(135deg, #00a8cc 0%, #0077be 100%);
//   color: #ffffff;
//   font-size: 0.9rem;
//   font-weight: 700;
//   padding: 0.5rem 1rem;
//   border-radius: 20px;
//   margin-bottom: 1rem;
//   text-transform: uppercase;
//   letter-spacing: 1px;
// }

// .stepTitle {
//   font-size: 1.3rem;
//   font-weight: 700;
//   color: #ffffff;
//   margin: 0 0 1rem 0;
//   background: linear-gradient(135deg, #ffffff 0%, #00a8cc 100%);
//   -webkit-background-clip: text;
//   -webkit-text-fill-color: transparent;
//   background-clip: text;
// }

// .stepDescription {
//   font-size: 0.95rem;
//   color: rgba(255, 255, 255, 0.8);
//   line-height: 1.6;
//   margin: 0;
// }

// // Step Connector
// .stepConnector {
//   position: absolute;
//   top: 40px;
//   right: -1rem;
//   width: 2rem;
//   height: 2px;
//   background: linear-gradient(90deg, rgba(0, 168, 204, 0.3) 0%, rgba(0, 168, 204, 0.1) 100%);
//   z-index: 1;
// }

// Responsive Design
@media (max-width: 1200px) {
  .journeySection {
    padding: 6rem 0;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .stepsContainer {
    gap: 1.5rem;
  }

  .step {
    padding: 1.75rem 0.75rem;
  }

  .stepIcon {
    width: 70px;
    height: 70px;
    margin-bottom: 1.5rem;
  }

  .icon {
    width: 35px;
    height: 35px;
  }

  .stepTitle {
    font-size: 1.2rem;
  }

  .stepDescription {
    font-size: 0.9rem;
  }
}

@media (max-width: 968px) {
  .journeySection {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .sectionHeader {
    margin-bottom: 4rem;
  }

  .sectionTitle {
    font-size: 2.25rem;
  }

  .sectionSubtitle {
    font-size: 1.1rem;
  }

  .stepsContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .step {
    padding: 2rem 1rem;
  }

  .stepConnector {
    display: none;
  }
}

@media (max-width: 768px) {
  .journeySection {
    padding: 4rem 0;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }

  .stepsContainer {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 400px;
  }

  .step {
    padding: 1.5rem 1rem;
  }

  .stepIcon {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }

  .icon {
    width: 30px;
    height: 30px;
  }

  .stepPhase {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .stepTitle {
    font-size: 1.1rem;
  }

  .stepDescription {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .journeySection {
    padding: 3rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .sectionTitle {
    font-size: 1.75rem;
  }

  .step {
    padding: 1.25rem 0.75rem;
  }

  .stepIcon {
    width: 50px;
    height: 50px;
  }

  .icon {
    width: 25px;
    height: 25px;
  }

  .stepPhase {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
  }

  .stepTitle {
    font-size: 1rem;
  }

  .stepDescription {
    font-size: 0.8rem;
  }
}
