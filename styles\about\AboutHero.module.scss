// About Hero Section - Simple & Futuristic
.aboutHero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: radial-gradient(
      ellipse at center top,
      rgba(0, 168, 204, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse at center bottom,
      rgba(0, 119, 190, 0.02) 0%,
      transparent 50%
    ),
   linear-gradient(135deg, #010204 0%, #020304 25%, #040507 50%, #020304 75%, #020306 100%);
  overflow: hidden;
  padding: 0;
}

// Futuristic Background Layer
.backgroundLayer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

// Geometric Shapes
.geometricShapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape1 {
  position: absolute;
  top: 20%;
  left: 10%;
  width: 200px;
  height: 200px;
  background: linear-gradient(
    45deg,
    rgba(0, 168, 204, 0.05) 0%,
    transparent 100%
  );
  border: 1px solid rgba(0, 168, 204, 0.1);
  border-radius: 50%;
  animation: float 15s ease-in-out infinite;
}

.shape2 {
  position: absolute;
  top: 60%;
  right: 15%;
  width: 150px;
  height: 150px;
  background: linear-gradient(
    135deg,
    rgba(0, 119, 190, 0.04) 0%,
    transparent 100%
  );
  border: 1px solid rgba(0, 119, 190, 0.08);
  transform: rotate(45deg);
  animation: rotate 20s linear infinite;
}

.shape3 {
  position: absolute;
  bottom: 20%;
  left: 20%;
  width: 100px;
  height: 100px;
  background: linear-gradient(
    90deg,
    rgba(0, 168, 204, 0.03) 0%,
    transparent 100%
  );
  border: 1px solid rgba(0, 168, 204, 0.06);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  animation: pulse 8s ease-in-out infinite;
}

// Glowing Lines
.glowingLines {
  position: absolute;
  width: 100%;
  height: 100%;
}

.line1 {
  position: absolute;
  top: 30%;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 168, 204, 0.3) 50%,
    transparent 100%
  );
  animation: lineGlow 6s ease-in-out infinite;
}

.line2 {
  position: absolute;
  top: 70%;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 119, 190, 0.2) 50%,
    transparent 100%
  );
  animation: lineGlow 8s ease-in-out infinite reverse;
}

.line3 {
  position: absolute;
  left: 50%;
  top: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(0, 168, 204, 0.15) 50%,
    transparent 100%
  );
  animation: lineGlow 10s ease-in-out infinite;
}

// Container
.container {
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Content
.content {
  text-align: center;
  max-width: 90%;
  width: 100%;
}

.title {
  font-size: 4rem;
  font-weight: 900;
  color: #ffffff;
  line-height: 1.1;
  margin: 0 0 2rem 0;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1.2s ease-out;
  background: linear-gradient(135deg, #ffffff 0%, #00a8cc 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 168, 204, 0.3);

  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

.description {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0 0 3rem 0;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
  font-weight: 300;
}

// Highlights
.highlights {
  display: flex;
  justify-content: center;
  gap: 4rem;
  margin-top: 3rem;
  
}

.highlight {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.25rem 1.5rem;
  background: rgba(0, 168, 204, 0.03);
  border: 1px solid rgba(0, 168, 204, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    background: rgba(0, 168, 204, 0.06);
    border-color: rgba(0, 168, 204, 0.2);
    box-shadow: 0 10px 30px rgba(0, 168, 204, 0.1);
  }
}

.number {
  font-size: 1.75rem;
  font-weight: 800;
  color: #00a8cc;
  line-height: 1;
}

.label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

// Animations
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(45deg);
  }
  to {
    transform: rotate(405deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes lineGlow {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .title {
    font-size: 4rem;
  }

  .description {
    font-size: 1.3rem;
  }

  .highlights {
    gap: 3rem;
  }
}

@media (max-width: 968px) {
  .container {
    padding: 0 1rem;
  }

  .title {
    font-size: 3.5rem;
  }

  .description {
    font-size: 1.2rem;
    max-width: 500px;
  }

  .highlights {
    gap: 2rem;
  }

  .highlight {
    padding: 1.25rem;
  }

  .number {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .title {
    font-size: 3rem;
  }

  .description {
    font-size: 1.1rem;
    max-width: 400px;
  }

  .highlights {
    flex-direction: column;
    gap: 1.5rem;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
  }

  .highlight {
    padding: 1rem;
  }

  .number {
    font-size: 1.8rem;
  }

  .label {
    font-size: 0.8rem;
  }

  // Hide some background elements on mobile
  .shape1,
  .shape2 {
    display: none;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1rem;
    max-width: 350px;
  }

  .highlight {
    padding: 0.75rem;
  }

  .number {
    font-size: 1.5rem;
  }

  .label {
    font-size: 0.75rem;
  }
}
