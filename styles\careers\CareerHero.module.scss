// CareerHero Section
.heroSection {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0a0a0a 0%, #020204 50%, #03050a 100%);
  overflow: hidden;
  padding: 2rem 0;
}

// Background Elements
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.gridPattern {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 300px;
  height: 300px;
  background-image: linear-gradient(
      rgba(59, 130, 246, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: 10px;
  opacity: 0.3;
  filter: blur(0.5px);
}

.glowOrb1 {
  position: absolute;
  top: 15%;
  right: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(59, 130, 246, 0.08) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(1px);
}

.glowOrb2 {
  position: absolute;
  bottom: 20%;
  left: 20%;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(16, 185, 129, 0.12) 0%,
    rgba(16, 185, 129, 0.06) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(1px);
}

.glowOrb3 {
  position: absolute;
  top: 60%;
  right: 25%;
  width: 100px;
  height: 100px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(99, 102, 241, 0.05) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(1px);
}

.techLines {
  position: absolute;
  top: 30%;
  left: 5%;
  width: 250px;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.4) 20%,
    rgba(16, 185, 129, 0.6) 50%,
    rgba(99, 102, 241, 0.4) 80%,
    transparent 100%
  );

  &::before {
    content: "";
    position: absolute;
    top: 50px;
    left: 0;
    width: 180px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(16, 185, 129, 0.3) 30%,
      rgba(59, 130, 246, 0.5) 70%,
      transparent 100%
    );
  }

  &::after {
    content: "";
    position: absolute;
    top: 100px;
    left: 30px;
    width: 120px;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(99, 102, 241, 0.4) 40%,
      rgba(16, 185, 129, 0.3) 80%,
      transparent 100%
    );
  }
}

.circuitPattern {
  position: absolute;
  bottom: 15%;
  right: 10%;
  width: 180px;
  height: 180px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    border: 1px solid rgba(16, 185, 129, 0.15);
    border-radius: 50%;
  }

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    border: 1px solid rgba(99, 102, 241, 0.1);
    border-radius: 50%;
  }
}

// Container
.container {
  position: relative;
  z-index: 2;
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

// Hero Content
.heroContent {
  max-width: 90%;
  margin: 0 auto;
}

.badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 50px;
  padding: 0.5rem 1.5rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.badgeText {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badgeGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.1) 50%,
    transparent 100%
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.heroTitle {
  font-size: 4rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
  line-height: 1.1;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.gradientText {
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
}

.heroSubtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 3rem 0;
  line-height: 1.6;
  max-width: 60%;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
}

// Stats Grid
.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin: 3rem auto;
  padding: 1rem;
  width: 50%;
}

.statItem {
  text-align: center;
  padding: 1rem;
}

.statNumber {
  font-size: 2rem;
  font-weight: 800;
  color: #ffffff;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
}

.statLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

// CTA Buttons
.ctaButtons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  margin-top: 3rem;
}

.primaryButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  color: #ffffff;
  font-size: 1.05rem;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;

  &:hover {
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.2), 0 0 30px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    .buttonGlow {
      opacity: 1;
      transform: scale(1.2);
    }

    .buttonIcon {
      transform: translateX(4px);
    }
  }
}

.secondaryButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.05rem;
  font-weight: 600;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 0 20px rgba(59, 130, 246, 0.2);

    .buttonIcon {
      transform: translateX(3px);
    }
  }
}

.buttonText {
  position: relative;
  z-index: 2;
}

.buttonGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s ease;
}

.buttonIcon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

// Floating Animation
.floatingElement {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(1deg);
  }
  50% {
    transform: translateY(-5px) rotate(-1deg);
  }
  75% {
    transform: translateY(-15px) rotate(0.5deg);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .container {
    max-width: 95vw;
    padding: 0 1.5rem;
  }

  .heroTitle {
    font-size: 3.5rem;
  }

  .statsGrid {
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .statNumber {
    font-size: 2.2rem;
  }

  .gridPattern {
    width: 250px;
    height: 250px;
  }

  .glowOrb1 {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 968px) {
  .heroSection {
    min-height: 90vh;
    padding: 1.5rem 0;
  }

  .heroTitle {
    font-size: 3rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin: 2rem 0;
    padding: 1.5rem;
  }

  .statItem {
    padding: 0.75rem;
  }

  .statNumber {
    font-size: 2rem;
  }

  .ctaButtons {
    flex-direction: column;
    gap: 1rem;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .techLines {
    width: 200px;
  }

  .circuitPattern {
    width: 120px;
    height: 120px;
  }
}

@media (max-width: 768px) {
  .heroSection {
    min-height: 80vh;
    padding: 1rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .heroTitle {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .heroSubtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .badge {
    padding: 0.5rem 1rem;
    margin-bottom: 1.5rem;
  }

  .badgeText {
    font-size: 0.8rem;
  }

  .statsGrid {
    margin: 1.5rem 0;
    padding: 1rem;
  }

  .statNumber {
    font-size: 1.8rem;
  }

  .statLabel {
    font-size: 0.8rem;
  }

  .primaryButton,
  .secondaryButton {
    font-size: 1rem;
    padding: 0.875rem 2rem;
  }

  .buttonIcon {
    width: 18px;
    height: 18px;
  }

  // Reduce background elements for mobile
  .glowOrb2,
  .glowOrb3,
  .techLines,
  .circuitPattern {
    display: none;
  }

  .gridPattern {
    width: 150px;
    height: 150px;
    opacity: 0.2;
  }

  .glowOrb1 {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 0.95rem;
  }

  .statsGrid {
    padding: 0.75rem;
  }

  .statNumber {
    font-size: 1.5rem;
  }

  .primaryButton,
  .secondaryButton {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
  }

  .ctaButtons {
    margin-top: 2rem;
  }

  // Minimal background for very small screens
  .backgroundElements {
    opacity: 0.5;
  }
}
