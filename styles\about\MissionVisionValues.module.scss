// Mission, Vision, Values Section
.mvvSection {
  position: relative;
  padding: 8rem 0;
  background:
    radial-gradient(ellipse 800px 600px at top left, rgba(0, 168, 204, 0.04) 0%, rgba(0, 168, 204, 0.01) 40%, transparent 70%),
    radial-gradient(ellipse 600px 400px at bottom right, rgba(0, 119, 190, 0.03) 0%, rgba(0, 119, 190, 0.01) 40%, transparent 70%),
    radial-gradient(ellipse 400px 300px at center, rgba(0, 168, 204, 0.015) 0%, transparent 60%),
    linear-gradient(135deg, #010204 0%, #020304 25%, #040507 50%, #020304 75%, #020306 100%);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      linear-gradient(90deg, transparent 0%, rgba(0, 168, 204, 0.008) 50%, transparent 100%),
      linear-gradient(0deg, transparent 0%, rgba(0, 119, 190, 0.006) 50%, transparent 100%);
    background-size: 200px 100%, 100% 200px;
    animation: backgroundShift 20s ease-in-out infinite;
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(circle at 20% 30%, rgba(0, 168, 204, 0.04) 1px, transparent 1px),
      radial-gradient(circle at 80% 70%, rgba(0, 119, 190, 0.03) 1px, transparent 1px),
      radial-gradient(circle at 40% 80%, rgba(0, 168, 204, 0.025) 1px, transparent 1px);
    background-size: 150px 150px, 200px 200px, 180px 180px;
    animation: particleFloat 15s ease-in-out infinite;
    pointer-events: none;
    opacity: 0.3;
  }
}

// Enhanced Background Elements
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floatingShape1 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 120px;
  height: 120px;
  background: linear-gradient(45deg, rgba(0, 168, 204, 0.03) 0%, rgba(0, 168, 204, 0.008) 100%);
  border: 1px solid rgba(0, 168, 204, 0.06);
  border-radius: 50%;
  animation: floatSlow 18s ease-in-out infinite;
  filter: blur(0.5px);
}

.floatingShape2 {
  position: absolute;
  top: 70%;
  right: 12%;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(0, 119, 190, 0.025) 0%, rgba(0, 119, 190, 0.006) 100%);
  border: 1px solid rgba(0, 119, 190, 0.05);
  transform: rotate(45deg);
  animation: floatMedium 14s ease-in-out infinite reverse;
  filter: blur(0.5px);
}

.floatingShape3 {
  position: absolute;
  bottom: 25%;
  left: 15%;
  width: 60px;
  height: 60px;
  background: linear-gradient(90deg, rgba(0, 168, 204, 0.02) 0%, rgba(0, 168, 204, 0.004) 100%);
  border: 1px solid rgba(0, 168, 204, 0.04);
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  animation: floatFast 12s ease-in-out infinite;
  filter: blur(0.5px);
}

.glowingOrb1 {
  position: absolute;
  top: 40%;
  right: 20%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(0, 168, 204, 0.025) 0%, rgba(0, 168, 204, 0.008) 40%, transparent 70%);
  border-radius: 50%;
  animation: orbPulse 16s ease-in-out infinite;
  filter: blur(1px);
}

.glowingOrb2 {
  position: absolute;
  bottom: 30%;
  right: 8%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(0, 119, 190, 0.018) 0%, rgba(0, 119, 190, 0.005) 40%, transparent 70%);
  border-radius: 50%;
  animation: orbPulse 20s ease-in-out infinite reverse;
  filter: blur(1px);
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  position: relative;
  z-index: 2;
}

// Section Header
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #00a8cc 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

// Cards Grid
.cardsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

// Individual Card
.card {
  position: relative;
  padding: 3rem 2rem;
  border-radius: 24px;
  backdrop-filter: blur(15px);
  transition: all 0.4s ease;
  overflow: hidden;
  cursor: pointer;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 168, 204, 0.4) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  &:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(0, 168, 204, 0.2);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.5),
      0 0 40px rgba(0, 168, 204, 0.08);

    &::before {
      opacity: 1;
    }

    .cardGlow {
      opacity: 1;
      transform: scale(1.1);
    }

    .icon {
      transform: scale(1.2) rotate(10deg);
    }
  }
}

// Card Icon
.cardIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 2rem auto;
  background: rgba(0, 168, 204, 0.05);
  border: 2px solid rgba(0, 168, 204, 0.1);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.iconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.icon {
  width: 40px;
  height: 40px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.4s ease;
  filter: drop-shadow(0 0 10px rgba(0, 168, 204, 0.3));
}

// Card Content
.cardContent {
  text-align: center;
}

.cardTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #00a8cc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cardDescription {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin: 0;
}

// Card Glow Effect
.cardGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(0, 168, 204, 0.04) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.4s ease;
  pointer-events: none;
  z-index: -1;
}

// Background Animations
@keyframes backgroundShift {
  0%, 100% {
    transform: translateX(0) translateY(0);
    opacity: 0.15;
  }
  25% {
    transform: translateX(20px) translateY(-10px);
    opacity: 0.25;
  }
  50% {
    transform: translateX(-10px) translateY(15px);
    opacity: 0.2;
  }
  75% {
    transform: translateX(15px) translateY(-5px);
    opacity: 0.3;
  }
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-15px) rotate(120deg);
    opacity: 0.4;
  }
  66% {
    transform: translateY(10px) rotate(240deg);
    opacity: 0.2;
  }
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotate(180deg);
  }
  75% {
    transform: translateY(15px) translateX(5px) rotate(270deg);
  }
}

@keyframes floatMedium {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(45deg);
  }
  33% {
    transform: translateY(-15px) translateX(-10px) rotate(165deg);
  }
  66% {
    transform: translateY(10px) translateX(12px) rotate(285deg);
  }
}

@keyframes floatFast {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
  }
  50% {
    transform: translateY(-25px) translateX(8px) scale(1.1);
  }
}

@keyframes orbPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.15;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .mvvSection {
    padding: 6rem 0;
  }
  
  .sectionTitle {
    font-size: 2.5rem;
  }
  
  .cardsGrid {
    gap: 1.5rem;
  }
  
  .card {
    padding: 2.5rem 1.5rem;
  }
}

@media (max-width: 968px) {
  .mvvSection {
    padding: 5rem 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .sectionHeader {
    margin-bottom: 3rem;
  }
  
  .sectionTitle {
    font-size: 2.25rem;
  }
  
  .sectionSubtitle {
    font-size: 1.1rem;
  }
  
  .cardsGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .card {
    padding: 2rem 1.5rem;
  }
  
  .cardIcon {
    width: 70px;
    height: 70px;
    margin-bottom: 1.5rem;
  }
  
  .icon {
    width: 35px;
    height: 35px;
  }
  
  .cardTitle {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }
  
  .cardDescription {
    font-size: 0.95rem;
  }
}

@media (max-width: 768px) {
  .mvvSection {
    padding: 4rem 0;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .sectionSubtitle {
    font-size: 1rem;
  }
  
  .card {
    padding: 1.5rem 1rem;
  }
  
  .cardIcon {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }
  
  .icon {
    width: 30px;
    height: 30px;
  }
  
  .cardTitle {
    font-size: 1.1rem;
  }
  
  .cardDescription {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .mvvSection {
    padding: 3rem 0;
  }
  
  .sectionHeader {
    margin-bottom: 2rem;
  }
  
  .sectionTitle {
    font-size: 1.75rem;
  }
  
  .cardsGrid {
    gap: 1.5rem;
  }
  
  .card {
    padding: 1.25rem 0.75rem;
  }
  
  .cardIcon {
    width: 50px;
    height: 50px;
  }
  
  .icon {
    width: 25px;
    height: 25px;
  }
  
  .cardTitle {
    font-size: 1rem;
  }
  
  .cardDescription {
    font-size: 0.85rem;
  }
}
