.container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  overflow: hidden;
  background: var(--black);
}

.heroContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1400px;
  width: 100%;
  align-items: center;
  z-index: 10;
  position: relative;
}

.textSection {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding-right: 2rem;
}

.badge {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, rgba(0, 69, 84, 0.2), rgba(0, 69, 84, 0.1));
  border: 1px solid rgba(0, 69, 84, 0.3);
  border-radius: 50px;
  color: var(--white);
  font-family: 'satoshi', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  width: fit-content;
}

.title {
  font-family: 'satoshi', sans-serif;
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  color: var(--white);
  margin: 0;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

  .gradient {
    background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
    filter: drop-shadow(0 2px 10px rgba(0, 168, 204, 0.3));
  }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.description {
  font-family: 'manrope', sans-serif;
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--parawhite);
  margin: 0;
  max-width: 90%;
}

.ctaButtons {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  margin-top: 1rem;
}

.primaryBtn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #00a8cc, #0077be);
  color: var(--white);
  border: none;
  border-radius: 50px;
  font-family: 'satoshi', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 168, 204, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 168, 204, 0.4);

    .arrow {
      transform: translateX(4px);
    }
  }

  .arrow {
    transition: transform 0.3s ease;
    font-size: 1.2rem;
  }
}

.secondaryBtn {
  padding: 1rem 2rem;
  background: transparent;
  color: var(--white);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  font-family: 'satoshi', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
  }
}

// Services grid styles moved to ServicesGrid.module.scss

// Background Elements
.backgroundElements {
  position: absolute;
  inset: 0;
  z-index: 1;
  pointer-events: none;
}

.gradientOrb1 {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 168, 204, 0.15) 0%, transparent 70%);
  border-radius: 50%;
  filter: blur(40px);
  animation: float 6s ease-in-out infinite, pulse 4s ease-in-out infinite;
}

.gradientOrb2 {
  position: absolute;
  bottom: 20%;
  right: 15%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(0, 119, 190, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  filter: blur(30px);
  animation: float 8s ease-in-out infinite reverse, pulse 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

.gridPattern {
  position: absolute;
  inset: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

// Responsive Design
@media (max-width: 1200px) {
  .title {
    font-size: 3.5rem;
  }

  .heroContent {
    gap: 3rem;
  }
}

@media (max-width: 968px) {
  .heroContent {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .textSection {
    padding-right: 0;
    order: 1;
  }

  .title {
    font-size: 3rem;
  }

  .description {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .title {
    font-size: 2.5rem;
  }

  .description {
    font-size: 1.1rem;
  }

  .ctaButtons {
    flex-direction: column;
    gap: 1rem;

    button {
      width: 100%;
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2rem;
  }

  .description {
    font-size: 1rem;
  }

  .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  .primaryBtn, .secondaryBtn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
}