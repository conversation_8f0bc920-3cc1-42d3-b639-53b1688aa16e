// Contact Hero Section
.contactHeroSection {
  position: relative;
  min-height: 100vh;
  padding: 8rem 0 4rem 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #020204 50%, #03050a 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
}

// Background Elements
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.techGrid {
  position: absolute;
  top: 10%;
  right: 8%;
  width: 300px;
  height: 300px;
  background-image: linear-gradient(
      rgba(59, 130, 246, 0.12) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(59, 130, 246, 0.12) 1px, transparent 1px);
  background-size: 30px 30px;
  border-radius: 20px;
  opacity: 0.6;
  filter: blur(0.5px);
}

.glowOrb1 {
  position: absolute;
  top: 15%;
  left: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(59, 130, 246, 0.08) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

.glowOrb2 {
  position: absolute;
  bottom: 20%;
  right: 15%;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(16, 185, 129, 0.12) 0%,
    rgba(16, 185, 129, 0.06) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

.glowOrb3 {
  position: absolute;
  top: 60%;
  left: 5%;
  width: 120px;
  height: 120px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.1) 0%,
    rgba(99, 102, 241, 0.05) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

.circuitLines {
  position: absolute;
  top: 40%;
  right: 5%;
  width: 250px;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.6) 20%,
    rgba(16, 185, 129, 0.8) 50%,
    rgba(99, 102, 241, 0.6) 80%,
    transparent 100%
  );

  &::before {
    content: "";
    position: absolute;
    top: 50px;
    left: 30px;
    width: 180px;
    height: 3px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(16, 185, 129, 0.4) 30%,
      rgba(59, 130, 246, 0.6) 70%,
      transparent 100%
    );
  }
}

.dataStream {
  position: absolute;
  bottom: 25%;
  left: 8%;
  width: 3px;
  height: 150px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(59, 130, 246, 0.6) 30%,
    rgba(16, 185, 129, 0.8) 70%,
    transparent 100%
  );
  animation: dataFlow 4s ease-in-out infinite;
}

@keyframes dataFlow {
  0%,
  100% {
    opacity: 0.4;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.3);
  }
}

// Container
.container {
  position: relative;
  z-index: 2;
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

// Hero Grid
.heroGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

// Left Content
.leftContent {
  max-width: 600px;
}

.badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(59, 130, 246, 0.4);
  border-radius: 50px;
  padding: 0.5rem 1.25rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(15px);
  overflow: hidden;
}

.badgeText {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badgeGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.2) 50%,
    transparent 100%
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.heroTitle {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
  line-height: 1.1;
  text-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
}

.gradientText {
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
}

.heroSubtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 3rem 0;
  line-height: 1.6;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
  text-align: start;
}

// Contact Methods
.contactMethods {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.contactMethod {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 15px;
  padding: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-3px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2),
      0 0 20px rgba(59, 130, 246, 0.15);

    .methodIcon {
      transform: scale(1.1);
      background: rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.3);
    }
  }
}

.visitUsCard {
  grid-column: 1 / -1; // Span the entire width
}

.methodIcon {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  svg {
    width: 24px;
    height: 24px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.methodContent {
  flex: 1;
  text-align: left;
}

.methodTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 0.25rem 0;
}

.methodText {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  text-align: start;
}

// Response Time
.responseTime {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 50px;
  padding: 1rem 2rem;
  backdrop-filter: blur(10px);
}

.responseIcon {
  width: 20px;
  height: 20px;
  color: rgba(16, 185, 129, 0.8);

  svg {
    width: 100%;
    height: 100%;
  }
}

.responseText {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

// Right Form
.rightForm {
  padding-left: 2rem;
}

.formContainer {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(10px);
  height: fit-content;
}

.formHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.formTitle {
  font-size: 1.75rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 0.75rem 0;
  line-height: 1.2;
}

.formSubtitle {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.5;
}

.contactForm {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formLabel {
  font-size: 0.85rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.formInput,
.formSelect,
.formTextarea {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 0.875rem 1rem;
  font-size: 0.95rem;
  color: #ffffff;
  transition: all 0.3s ease;
  font-family: inherit;

  &::placeholder {
    color: rgba(255, 255, 255, 0.4);
  }

  &:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

.formSelect {
  cursor: pointer;

  option {
    background: #1a1a1a;
    color: #ffffff;
  }
}

.formTextarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

.submitButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
  margin-top: 0.5rem;
  width: 100%;

  &:hover:not(:disabled) {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 15px 30px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      0 0 25px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    .buttonGlow {
      opacity: 1;
      transform: scale(1.2);
    }

    .buttonIcon {
      transform: translateX(3px);
    }
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.buttonText {
  position: relative;
  z-index: 2;
}

.buttonGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle,
    rgba(255, 255, 255, 0.15) 0%,
    transparent 70%
  );
  border-radius: 50px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s ease;
}

.buttonIcon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Floating Animation
.floatingElement {
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) rotate(1deg);
  }
  50% {
    transform: translateY(-8px) rotate(-1deg);
  }
  75% {
    transform: translateY(-20px) rotate(0.5deg);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .container {
    max-width: 95vw;
    padding: 0 1.5rem;
  }

  .heroTitle {
    font-size: 4rem;
  }

  .heroSubtitle {
    font-size: 1.2rem;
  }

  .contactMethods {
    gap: 1.5rem;
  }

  .techGrid {
    width: 250px;
    height: 250px;
  }

  .glowOrb1 {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 968px) {
  .contactHeroSection {
    min-height: 90vh;
    padding: 6rem 0 3rem 0;
  }

  .heroGrid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .leftContent {
    max-width: 100%;
    text-align: center;
  }

  .rightForm {
    padding-left: 0;
    order: 2;
  }

  .formContainer {
    padding: 2rem;
  }

  .heroTitle {
    font-size: 3.5rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
  }

  .contactMethods {
    grid-template-columns: 1fr;
    gap: 1.25rem;
    margin-bottom: 2.5rem;
  }

  .visitUsCard {
    grid-column: 1; // Reset to single column on mobile
  }

  .contactMethod {
    padding: 1.25rem;
  }

  .formRow {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .formTitle {
    font-size: 1.5rem;
  }

  // Reduce background elements
  .glowOrb3,
  .dataStream {
    display: none;
  }

  .circuitLines {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .contactHeroSection {
    min-height: 80vh;
    padding: 5rem 0 2rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .heroGrid {
    gap: 2.5rem;
  }

  .formContainer {
    padding: 1.5rem;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .contactMethod {
    padding: 1rem;
    gap: 0.75rem;
  }

  .methodIcon {
    width: 40px;
    height: 40px;

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .methodTitle {
    font-size: 1rem;
  }

  .methodText {
    font-size: 0.9rem;
  }

  .responseTime {
    padding: 0.875rem 1.5rem;
  }

  .responseText {
    font-size: 0.85rem;
  }

  .formTitle {
    font-size: 1.25rem;
  }

  .formSubtitle {
    font-size: 0.9rem;
  }

  .formInput,
  .formSelect,
  .formTextarea {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .submitButton {
    font-size: 0.95rem;
    padding: 0.875rem 2rem;
  }

  // Further reduce background elements
  .glowOrb2,
  .circuitLines {
    display: none;
  }

  .techGrid {
    width: 150px;
    height: 150px;
    opacity: 0.3;
  }

  .glowOrb1 {
    width: 100px;
    height: 100px;
    opacity: 0.5;
  }
}

@media (max-width: 480px) {
  .contactHeroSection {
    min-height: 70vh;
    padding: 4rem 0 2rem 0;
  }

  .heroGrid {
    gap: 2rem;
  }

  .formContainer {
    padding: 1.25rem;
  }

  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 0.95rem;
  }

  .contactMethod {
    padding: 0.875rem;
    gap: 0.5rem;
  }

  .methodIcon {
    width: 36px;
    height: 36px;

    svg {
      width: 18px;
      height: 18px;
    }
  }

  .methodTitle {
    font-size: 0.95rem;
  }

  .methodText {
    font-size: 0.85rem;
  }

  .responseTime {
    padding: 0.75rem 1.25rem;
    gap: 0.5rem;
  }

  .responseIcon {
    width: 18px;
    height: 18px;
  }

  .responseText {
    font-size: 0.8rem;
  }

  .formHeader {
    margin-bottom: 1.5rem;
  }

  .formTitle {
    font-size: 1.1rem;
  }

  .formSubtitle {
    font-size: 0.85rem;
  }

  .contactForm {
    gap: 1rem;
  }

  .formInput,
  .formSelect,
  .formTextarea {
    padding: 0.625rem;
    font-size: 0.85rem;
  }

  .formTextarea {
    min-height: 80px;
  }

  .submitButton {
    font-size: 0.9rem;
    padding: 0.75rem 1.75rem;
  }

  // Minimal background for very small screens
  .backgroundElements {
    opacity: 0.4;
  }
}
