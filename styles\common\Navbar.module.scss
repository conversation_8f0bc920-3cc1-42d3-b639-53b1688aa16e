.container {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 2rem;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 999;
  background-color: var(--white);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  .title {
    font-weight: 700;
    font-size: 2rem;
  }

  & div {
    display: flex;
    gap: 1rem;
  }

  svg {
    transform: scale(1.1);
  }

  a {
    color: var(--para);
    position: relative; /* Needed for absolute positioning of ::after */
    text-decoration: none;
    transition: color 0.25s ease-in-out;
  }

  a::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -2px; /* Adjust distance below text */
    width: 0%; /* Start with zero width */
    height: 2px; /* Thickness of the underline */
    background-color: var(--para);
    transition: width 0.3s ease-in-out;
  }

  a:hover {
    color: var(--black);
  }

  a:hover::after {
    width: 100%; /* Expands fully on hover */
  }

  .active {
    color: var(--black);
  }

  .active::after {
    width: 100%;
    background-color: var(--black);
  }
}

.navItem{
  color: var(--white) !important;
  font-size: 1rem;

  span{
     color: var(--white) !important;
     font-size: 0.9rem;
  }
}

.activeNavItem {
  padding: 1.25rem 1.25rem;
  font-weight: 600;

  span {
    font-weight: 600;
  }
}

.shineBtn {
  position: relative;
  background-color: rgb(5, 5, 5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.5rem;
  gap: 10px;
  border-radius: 2rem;
  outline: none;
  overflow: hidden;
  border: 2px solid #3d3d3d6c;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.shineBtn:hover {
  transform: scale(1.05);
  color: rgb(211, 211, 211);
  background: #2b2b2bb1;
}

.shineBtn:hover::before {
  animation: shine 1.5s ease-out infinite;
}

.shineBtn::before {
  content: "";
  position: absolute;
  width: 100px;
  height: 100%;
  background-image: linear-gradient(120deg,
      rgba(255, 255, 255, 0) 30%,
      rgba(255, 255, 255, 0.336),
      rgba(255, 255, 255, 0) 70%);
  top: 0;
  left: -100px;
  opacity: 0.6;
}

@keyframes shine {
  0% {
    left: -100px;
  }

  60% {
    left: 100%;
  }

  to {
    left: 100%;
  }
}