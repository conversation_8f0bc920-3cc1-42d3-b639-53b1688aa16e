// CTA Section
.ctaSection {
  position: relative;
  padding: 8rem 0;
  background: 
    radial-gradient(ellipse 800px 400px at center top, rgba(0, 168, 204, 0.02) 0%, rgba(0, 168, 204, 0.005) 40%, transparent 70%),
    radial-gradient(ellipse 600px 300px at center bottom, rgba(0, 119, 190, 0.015) 0%, rgba(0, 119, 190, 0.003) 40%, transparent 70%),
    linear-gradient(135deg, #010204 0%, #020304 25%, #040507 50%, #020304 75%, #020306 100%);
  overflow: hidden;
}

// Container
.container {
  position: relative;
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  z-index: 2;
}

// CTA Content
.ctaContent {
  text-align: center;
  max-width: 90vw;
  margin: 0 auto;
  position: relative;
  z-index: 3;
}

.ctaHeading {
  font-size: 3rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
  line-height: 1.1;
  background: linear-gradient(135deg, #ffffff 0%, #00a8cc 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ctaSubheading {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 3rem 0;
  line-height: 1.6;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
}

// CTA Button
.ctaButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #00a8cc, #0077be);
  color: #ffffff;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.75rem 1.75rem;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;

  &:hover {
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(135deg, #00a8cc, #0077be);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.2), 0 0 30px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    .buttonGlow {
      opacity: 1;
      transform: scale(1.2);
    }

    .buttonIcon {
      transform: translateX(4px);
    }
  }

  &:active {
    transform: translateY(-1px) scale(1.01);
  }
}

.buttonText {
  position: relative;
  z-index: 2;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.buttonIcon {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.buttonGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(16, 185, 129, 0.2) 50%,
    transparent 70%
  );
  border-radius: 50px;
  opacity: 0;
  transition: all 0.4s ease;
  z-index: 1;
  pointer-events: none;
}

// Background Elements
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.gradientOrb1 {
  position: absolute;
  top: 20%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(59, 130, 246, 0.05) 40%,
    transparent 70%
  );
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
  filter: blur(1px);
}

.gradientOrb2 {
  position: absolute;
  top: 60%;
  right: 15%;
  width: 250px;
  height: 250px;
  background: radial-gradient(
    circle,
    rgba(16, 185, 129, 0.08) 0%,
    rgba(16, 185, 129, 0.04) 40%,
    transparent 70%
  );
  border-radius: 50%;
  animation: float 10s ease-in-out infinite reverse;
  filter: blur(1px);
}

.gradientOrb3 {
  position: absolute;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.06) 0%,
    rgba(99, 102, 241, 0.03) 40%,
    transparent 70%
  );
  border-radius: 50%;
  animation: float 12s ease-in-out infinite;
  filter: blur(1px);
}

// Animations
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(1deg);
  }
  50% {
    transform: translateY(-10px) rotate(-1deg);
  }
  75% {
    transform: translateY(-15px) rotate(0.5deg);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .ctaSection {
    padding: 6rem 0;
  }

  .ctaHeading {
    font-size: 3rem;
  }

  .ctaSubheading {
    font-size: 1.2rem;
  }

  .gradientOrb1 {
    width: 250px;
    height: 250px;
  }

  .gradientOrb2 {
    width: 200px;
    height: 200px;
  }

  .gradientOrb3 {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 968px) {
  .ctaSection {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .ctaHeading {
    font-size: 2.5rem;
  }

  .ctaSubheading {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
  }

  .ctaButton {
    font-size: 1rem;
    padding: 0.9rem 2rem;
  }

  .gradientOrb1,
  .gradientOrb2 {
    display: none;
  }

  .gradientOrb3 {
    width: 120px;
    height: 120px;
  }
}

@media (max-width: 768px) {
  .ctaSection {
    padding: 4rem 0;
  }

  .ctaHeading {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .ctaSubheading {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .ctaButton {
    font-size: 0.95rem;
    padding: 0.8rem 1.8rem;
    gap: 0.5rem;
  }

  .buttonIcon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .ctaSection {
    padding: 3rem 0;
  }

  .ctaHeading {
    font-size: 1.75rem;
  }

  .ctaSubheading {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .ctaButton {
    font-size: 0.9rem;
    padding: 0.75rem 1.5rem;
  }

  .gradientOrb3 {
    width: 100px;
    height: 100px;
  }
}
