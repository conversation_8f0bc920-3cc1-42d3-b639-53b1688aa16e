.container{
  position: relative;
}

.card {
  position: relative;
  width:30rem;
  height: 25rem;
  padding: 2rem;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  border: none;

  &:hover {
    transform: translateY(-16px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }
}

// Accenture Color Themes
.red {
  background: linear-gradient(135deg, #e2062e 0%, #c8052a 100%);
  
  &:hover {
    background: linear-gradient(135deg, #e2062e 0%, #b8041f 100%);
  }
}

.blue {
  background: linear-gradient(135deg, #0066cc 0%, #004499 100%);
  &:hover {
    background: linear-gradient(135deg, #0066cc 0%, #003377 100%);
  }
}

.purple {
  background: linear-gradient(135deg, #460073 0%, #350055 100%);
  &:hover {
    background: linear-gradient(135deg, #460073 0%, #240033 100%);
  }
}

.backgroundLines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

// PATTERN 1: Organic Curved Lines
.pattern1 {
  .line {
    position: absolute;
    border: 1px solid rgb(195, 195, 195);
    border-radius: 50px;
    transform-origin: center;
    transition: all 0.4s ease;
  }

  .line1 {
    top: 15%;
    left: 20%;
    width: 120px;
    height: 60px;
    border-radius: 60px 20px;
    transform: rotate(-20deg);
  }

  .line2 {
    top: 35%;
    right: 15%;
    width: 80px;
    height: 40px;
    border-radius: 40px 60px;
    transform: rotate(45deg);
  }

  .line3 {
    bottom: 30%;
    left: 10%;
    width: 100px;
    height: 50px;
    border-radius: 50px 30px;
    transform: rotate(15deg);
  }

  .line4 {
    top: 55%;
    left: 45%;
    width: 70px;
    height: 35px;
    border-radius: 35px 50px;
    transform: rotate(-35deg);
  }

  .line5 {
    bottom: 20%;
    right: 20%;
    width: 90px;
    height: 45px;
    border-radius: 45px 25px;
    transform: rotate(60deg);
  }

  .line6 {
    top: 25%;
    left: 55%;
    width: 65px;
    height: 32px;
    border-radius: 32px 48px;
    transform: rotate(-50deg);
  }
}

// PATTERN 2: Circular/Arc Lines
.pattern2 {
  .circularLine {
    position: absolute;
    border: 1px solid rgb(185, 185, 185);
    border-radius: 50%;
    transform-origin: center;
    transition: all 0.5s ease;
  }

  .circular1 {
    top: 10%;
    left: 15%;
    width: 80px;
    height: 80px;
    border-top: none;
    border-right: none;
    transform: rotate(45deg);
  }

  .circular2 {
    top: 20%;
    right: 20%;
    width: 60px;
    height: 60px;
    border-bottom: none;
    border-left: none;
    transform: rotate(-30deg);
  }

  .circular3 {
    bottom: 25%;
    left: 25%;
    width: 100px;
    height: 100px;
    border-top: none;
    border-left: none;
    transform: rotate(15deg);
  }

  .circular4 {
    top: 50%;
    left: 50%;
    width: 70px;
    height: 70px;
    border-bottom: none;
    border-right: none;
    transform: rotate(75deg) translateX(-50%) translateY(-50%);
  }

  .circular5 {
    bottom: 15%;
    right: 15%;
    width: 90px;
    height: 90px;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
  }
}

// PATTERN 3: Wave/Flow Lines
.pattern3 {
  .waveLine {
    position: absolute;
    height: 1px;
    border: 1px solid rgb(185, 185, 185);
    transform-origin: left center;
    transition: all 0.4s ease;
  }

  .wave1 {
    top: 15%;
    left: 10%;
    width: 150px;
    border-radius: 50px;
    transform: rotate(-10deg);
  }

  .wave2 {
    top: 25%;
    right: 20%;
    width: 120px;
    border-radius: 60px;
    transform: rotate(25deg);
  }

  .wave3 {
    top: 40%;
    left: 20%;
    width: 100px;
    border-radius: 50px;
    transform: rotate(5deg);
  }

  .wave4 {
    top: 55%;
    right: 15%;
    width: 130px;
    border-radius: 65px;
    transform: rotate(-20deg);
  }

  .wave5 {
    bottom: 30%;
    left: 15%;
    width: 110px;
    border-radius: 55px;
    transform: rotate(15deg);
  }

  .wave6 {
    bottom: 20%;
    right: 25%;
    width: 90px;
    border-radius: 45px;
    transform: rotate(-30deg);
  }

  .wave7 {
    top: 70%;
    left: 40%;
    width: 80px;
    border-radius: 40px;
    transform: rotate(40deg);
  }
}

.lottieBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  transition: opacity 0.3s ease;
}

.content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.titleContainer {
  position: relative;
  height: 80px;
  overflow: hidden;
}

.title {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.descriptionContainer {
  position: absolute;
  left: 0;
  right: 0;
  transform: translateY(-100%);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  button{
    align-self: flex-end;
    font-size: 1.25rem;
  }
}

.description {
  font-size: 1.5rem;
  text-align: start;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.6;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

// .glowEffect {
//   position: absolute;
//   top: -2px;
//   left: -2px;
//   right: -2px;
//   bottom: -2px;
//   background: rgba(255, 255, 255, 0.1);
//   border-radius: 18px;
//   z-index: -1;
//   filter: blur(8px);
// }

@media (max-width: 768px) {
  .card {
    height: 250px;
    padding: 1.5rem;
  }

  .title {
    font-size: 1.25rem;
  }

  .description {
    font-size: 0.9rem;
  }
}



