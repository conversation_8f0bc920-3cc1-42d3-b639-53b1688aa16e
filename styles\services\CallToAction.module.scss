// Call to Action Section Styles
.callToAction {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #101010 50%, #0a0a0a 100%);
  overflow: hidden;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

// Container
.container {
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
  
}

// Background Elements
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.gradientOrb1 {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(0, 168, 204, 0.15) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
}

.gradientOrb2 {
  position: absolute;
  top: 60%;
  right: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(0, 119, 190, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite reverse;
}

.gradientOrb3 {
  position: absolute;
  bottom: 20%;
  left: 60%;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(0, 168, 204, 0.08) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 10s ease-in-out infinite;
}

.gridPattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 168, 204, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 168, 204, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

// Main CTA Content
.ctaContent {
  text-align: center;
  margin-bottom: 3rem;
}

.ctaHeader {
  margin-bottom: 2rem;
}

.handshakeIcon {
  position: relative;
  display: inline-block;
  margin-bottom: 0rem;
}

.iconGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(0, 168, 204, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 3s ease-in-out infinite;
}

.mainIcon {
  position: relative;
  font-size: 4rem;
  display: block;
  animation: float 4s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.5));
}

.ctaTitle {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #00a8cc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.ctaDescription {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 90%;
  margin: 0 auto;
  line-height: 1.6;
}

// Features Grid
.featuresGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
  max-width: 100%px;
  margin-left: auto;
  margin-right: auto;
}

.featureCard {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
  
  &:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 168, 204, 0.3);
    box-shadow: 0 10px 30px rgba(0, 168, 204, 0.1);
  }
}

.featureIcon {
  font-size: 2.5rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.featureContent {
  text-align: left;
}

.featureTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.featureDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  text-align: left;
}

// CTA Buttons
.ctaButtons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.primaryButton {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.75rem;
  background: linear-gradient(135deg, #00a8cc 0%, #0077be 100%);
  border: none;
  border-radius: 50px;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 168, 204, 0.4);
    
    .buttonGlow {
      opacity: 1;
    }
  }
}

.secondaryButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.75rem;
  background: transparent;
  border: 2px solid rgba(0, 168, 204, 0.5);
  border-radius: 50px;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 168, 204, 0.1);
    border-color: #00a8cc;
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 168, 204, 0.2);
  }
}

.buttonText {
  position: relative;
  z-index: 2;
}

.buttonIcon {
  font-size: 1.2rem;
  position: relative;
  z-index: 2;
}

.buttonGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

// Trust Indicators
.trustIndicators {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.trustItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.trustIcon {
  font-size: 1.2rem;
}

// Success Stats
.successStats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  padding: 2rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.statItem {
  text-align: center;
}

.statNumber {
  font-size: 2.5rem;
  font-weight: 700;
  color: #00a8cc;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #00a8cc 0%, #0077be 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.statLabel {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Animations
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .ctaTitle {
    font-size: 3rem;
  }
  
  .successStats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .callToAction {
    padding: 6rem 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .ctaTitle {
    font-size: 2.5rem;
  }
  
  .ctaDescription {
    font-size: 1.1rem;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .primaryButton,
  .secondaryButton {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .trustIndicators {
    gap: 1rem;
  }
  
  .successStats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .statNumber {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .ctaTitle {
    font-size: 2rem;
  }
  
  .mainIcon {
    font-size: 4rem;
  }
  
  .successStats {
    grid-template-columns: 1fr;
  }
}
