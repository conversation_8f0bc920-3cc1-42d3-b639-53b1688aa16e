.heroContainer {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #020204 50%, #03050a 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 2rem;
}

.backgroundCanvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.backgroundElements {
  position: absolute;
  inset: 0;
  z-index: 2;
  pointer-events: none;
}

.gridOverlay {
  position: absolute;
  inset: 0;
  background-image: linear-gradient(rgba(0, 168, 204, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 168, 204, 0.1) 1px, transparent 1px);
  background-size: 60px 60px;
  opacity: 0.3;
  animation: gridPulse 4s ease-in-out infinite;
}

@keyframes gridPulse {
  0%,
  100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}

.glowOrb1 {
  position: absolute;
  top: 15%;
  left: 10%;
  width: 400px;
  height: 400px;
  background: radial-gradient(
      circle at center,
      rgba(0, 69, 84, 0.5) -10%,
      rgba(5, 5, 5) 40%
    );
  border-radius: 50%;
  filter: blur(60px);
  animation: float 8s ease-in-out infinite;
}

.glowOrb2 {
  position: absolute;
  bottom: 20%;
  right: 15%;
  width: 300px;
  height: 300px;
  background: radial-gradient(
      circle at center,
      rgba(0, 69, 84, 0.5) -10%,
      rgba(5, 5, 5) 40%
    );
  border-radius: 50%;
  filter: blur(40px);
  animation: float 10s ease-in-out infinite reverse;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(10deg);
  }
}

.scanLine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: radial-gradient(
      circle at center,
      rgba(0, 69, 84, 0.5) -10%,
      rgba(5, 5, 5) 40%
    );
  animation: scan 3s linear infinite;
}

@keyframes scan {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.heroContent {
  position: relative;
  z-index: 10;
  display: grid;
  grid-template-columns: 70% 30%;
  gap: 2rem;
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  align-items: center;
}

.textSection {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1.25rem;
  background: rgba(0, 168, 204, 0.1);
  border: 1px solid rgba(0, 168, 204, 0.3);
  border-radius: 50px;
  color: rgba(0, 168, 204, 0.9);
  font-family: "satoshi", sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
  backdrop-filter: blur(10px);

  .badgeIcon {
    font-size: 1rem;
    animation: pulse 2s ease-in-out infinite;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.heroTitle {
  font-family: "satoshi", sans-serif;
  font-size: 3.75rem;
  font-weight: 700;
  line-height: 1.1;
  color: var(--white);
  margin: 0;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);

  .gradientText {
    background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
    filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
  }
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.heroDescription {
  font-family: "manrope", sans-serif;
  font-size: 1.15rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  max-width: 90%;
  text-align: start;
}

.ctaSection {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  margin-top: 1rem;
}

.primaryCta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.75rem;
  background: linear-gradient(135deg, #00a8cc, #0077be);
  color: var(--white);
  border: none;
  border-radius: 50px;
  font-family: "satoshi", sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 168, 204, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.6s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 168, 204, 0.4);

    &::before {
      left: 100%;
    }

    .ctaIcon {
      transform: translateX(4px);
    }
  }

  .ctaIcon {
    transition: transform 0.3s ease;
    font-size: 1.2rem;
  }
}

.secondaryCta {
  padding: 0.75rem 1.75rem;
  background: transparent;
  color: var(--white);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  font-family: "satoshi", sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    border-color: rgba(0, 168, 204, 0.5);
    background: rgba(0, 168, 204, 0.1);
    transform: translateY(-2px);
  }
}

.statsSection {
  display: flex;
  gap: 3rem;
  margin-top: 2rem;
}

.stat {
  text-align: left;

  .statNumber {
    font-family: "satoshi", sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: rgba(0, 168, 204, 0.9);
    line-height: 1;
    margin-bottom: 0.25rem;
  }

  .statLabel {
    font-family: "manrope", sans-serif;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.visualSection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.humanSilhouette {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 600px;
}

.silhouetteContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

.humanFigure {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 300px;
  background: linear-gradient(
    180deg,
    rgba(0, 168, 204, 0.3) 0%,
    rgba(0, 168, 204, 0.1) 100%
  );
  border-radius: 60px 60px 20px 20px;
  filter: blur(1px);

  &::before {
    content: "";
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: rgba(0, 168, 204, 0.4);
    border-radius: 50%;
    filter: blur(1px);
  }

  &::after {
    content: "";
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 120px;
    background: linear-gradient(
      180deg,
      rgba(0, 168, 204, 0.2) 0%,
      transparent 100%
    );
    border-radius: 40px 40px 20px 20px;
    filter: blur(1px);
  }
}

.dataInterface {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.dataPanel {
  position: absolute;
  background: rgba(0, 20, 40, 0.8);
  border: 1px solid rgba(0, 168, 204, 0.3);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  min-width: 120px;
  animation: panelFloat 4s ease-in-out infinite;

  &:nth-child(2) {
    animation-delay: -1.3s;
  }

  &:nth-child(3) {
    animation-delay: -2.6s;
  }
}

@keyframes panelFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.dataHeader {
  font-family: "satoshi", sans-serif;
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(0, 168, 204, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.dataChart {
  width: 80px;
  height: 40px;
  background: linear-gradient(
    45deg,
    rgba(0, 168, 204, 0.2) 0%,
    rgba(0, 168, 204, 0.4) 50%,
    rgba(0, 168, 204, 0.2) 100%
  );
  border-radius: 4px;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 168, 204, 0.6),
      transparent
    );
    animation: chartScan 2s linear infinite;
  }
}

@keyframes chartScan {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.dataMetrics {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metric {
  height: 8px;
  background: rgba(0, 168, 204, 0.3);
  border-radius: 4px;
  position: relative;
  overflow: hidden;

  &:nth-child(1) {
    width: 60px;
  }
  &:nth-child(2) {
    width: 45px;
  }
  &:nth-child(3) {
    width: 70px;
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(0, 168, 204, 0.8);
    border-radius: 4px;
    animation: metricFill 3s ease-in-out infinite;
  }

  &:nth-child(1)::before {
    width: 80%;
    animation-delay: 0s;
  }
  &:nth-child(2)::before {
    width: 65%;
    animation-delay: 0.5s;
  }
  &:nth-child(3)::before {
    width: 90%;
    animation-delay: 1s;
  }
}

@keyframes metricFill {
  0%,
  100% {
    transform: scaleX(0);
  }
  50% {
    transform: scaleX(1);
  }
}

.networkViz {
  width: 60px;
  height: 60px;
  position: relative;

  &::before,
  &::after {
    content: "";
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0, 168, 204, 0.8);
    border-radius: 50%;
    animation: networkPulse 2s ease-in-out infinite;
  }

  &::before {
    top: 10px;
    left: 10px;
  }

  &::after {
    bottom: 10px;
    right: 10px;
    animation-delay: 1s;
  }
}

@keyframes networkPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(0, 168, 204, 0.7);
  }
  50% {
    transform: scale(1.2);
    box-shadow: 0 0 0 10px rgba(0, 168, 204, 0);
  }
}

.connectionLines {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.connectionLine {
  position: absolute;
  width: 80px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 168, 204, 0.6) 50%,
    transparent 100%
  );
  animation: connectionPulse 3s ease-in-out infinite;

  &:nth-child(2) {
    animation-delay: 1s;
    transform: rotate(45deg);
  }

  &:nth-child(3) {
    animation-delay: 2s;
    transform: rotate(-30deg);
  }
}

@keyframes connectionPulse {
  0%,
  100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .heroTitle {
    font-size: 3.5rem;
  }

  .heroContent {
    gap: 3rem;
  }

  .humanSilhouette {
    max-width: 400px;
    height: 500px;
  }
}

@media (max-width: 968px) {
  .heroContainer {
    padding: 1rem;
  }

  .heroContent {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .textSection {
    order: 1;
  }

  .visualSection {
    order: 2;
  }

  .heroTitle {
    font-size: 3rem;
  }

  .heroDescription {
    max-width: 100%;
  }

  .statsSection {
    justify-content: center;
    gap: 2rem;
  }

  .humanSilhouette {
    max-width: 350px;
    height: 400px;
  }

  .glowOrb1,
  .glowOrb2 {
    width: 250px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: 2.5rem;
  }

  .heroDescription {
    font-size: 1.1rem;
  }

  .ctaSection {
    flex-direction: column;
    gap: 1rem;

    button {
      width: 100%;
      justify-content: center;
    }
  }

  .statsSection {
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;

    .stat {
      text-align: center;
    }
  }

  .humanSilhouette {
    max-width: 300px;
    height: 350px;
  }

  .dataPanel {
    min-width: 100px;
    padding: 0.75rem;
  }

  .dataChart {
    width: 60px;
    height: 30px;
  }
}

@media (max-width: 480px) {
  .heroTitle {
    font-size: 2rem;
  }

  .heroDescription {
    font-size: 1rem;
  }

  .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  .primaryCta,
  .secondaryCta {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .statsSection {
    gap: 1rem;
  }

  .stat {
    .statNumber {
      font-size: 1.5rem;
    }

    .statLabel {
      font-size: 0.8rem;
    }
  }

  .humanSilhouette {
    max-width: 250px;
    height: 300px;
  }

  .humanFigure {
    width: 80px;
    height: 200px;

    &::before {
      width: 40px;
      height: 40px;
      top: -30px;
    }

    &::after {
      width: 60px;
      height: 80px;
      bottom: -40px;
    }
  }
}
