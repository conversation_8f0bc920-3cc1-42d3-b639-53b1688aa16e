{"name": "first-us", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@lottiefiles/react-lottie-player": "^3.6.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.10.13", "@svgr/webpack": "^8.1.0", "@tabler/icons-react": "^3.34.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.23.6", "gsap": "^3.13.0", "lenis": "^1.3.4", "lottie-react": "^2.4.1", "lucide-react": "^0.483.0", "motion": "^12.23.6", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sass": "^1.86.0", "scss": "^0.2.4", "tailwind-merge": "^3.0.2", "three": "^0.180.0", "tw-animate-css": "^1.2.4", "web-vitals": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4"}}