// Final CTA Banner Section
.ctaBannerSection {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #020204 50%, #03050a 100%);
  overflow: hidden;
}

// Background Elements
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.techGrid {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background-image: linear-gradient(
      rgba(59, 130, 246, 0.15) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(59, 130, 246, 0.15) 1px, transparent 1px);
  background-size: 25px 25px;
  border-radius: 15px;
  opacity: 0.4;
  filter: blur(0.5px);
}

.glowOrb1 {
  position: absolute;
  top: 20%;
  right: 12%;
  width: 180px;
  height: 180px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.2) 0%,
    rgba(59, 130, 246, 0.1) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

.glowOrb2 {
  position: absolute;
  bottom: 25%;
  left: 15%;
  width: 140px;
  height: 140px;
  background: radial-gradient(
    circle,
    rgba(16, 185, 129, 0.18) 0%,
    rgba(16, 185, 129, 0.08) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

.glowOrb3 {
  position: absolute;
  top: 60%;
  right: 25%;
  width: 100px;
  height: 100px;
  background: radial-gradient(
    circle,
    rgba(99, 102, 241, 0.15) 0%,
    rgba(99, 102, 241, 0.07) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

.circuitLines {
  position: absolute;
  top: 35%;
  left: 5%;
  width: 200px;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.6) 20%,
    rgba(16, 185, 129, 0.8) 50%,
    rgba(99, 102, 241, 0.6) 80%,
    transparent 100%
  );

  &::before {
    content: "";
    position: absolute;
    top: 40px;
    left: 20px;
    width: 150px;
    height: 3px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(16, 185, 129, 0.4) 30%,
      rgba(59, 130, 246, 0.6) 70%,
      transparent 100%
    );
  }

  &::after {
    content: "";
    position: absolute;
    top: 80px;
    left: 40px;
    width: 100px;
    height: 3px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(99, 102, 241, 0.5) 40%,
      rgba(16, 185, 129, 0.4) 80%,
      transparent 100%
    );
  }
}

.hexagonPattern {
  position: absolute;
  bottom: 20%;
  right: 8%;
  width: 120px;
  height: 120px;
  background: transparent;
  border: 2px solid rgba(59, 130, 246, 0.2);
  clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border: 2px solid rgba(16, 185, 129, 0.15);
    clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
  }
}

.dataStream1 {
  position: absolute;
  top: 25%;
  right: 5%;
  width: 2px;
  height: 120px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(59, 130, 246, 0.6) 30%,
    rgba(16, 185, 129, 0.8) 70%,
    transparent 100%
  );
  animation: dataFlow 3s ease-in-out infinite;
}

.dataStream2 {
  position: absolute;
  bottom: 30%;
  left: 8%;
  width: 2px;
  height: 100px;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(16, 185, 129, 0.5) 30%,
    rgba(99, 102, 241, 0.7) 70%,
    transparent 100%
  );
  animation: dataFlow 4s ease-in-out infinite reverse;
}

@keyframes dataFlow {
  0%,
  100% {
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% {
    opacity: 1;
    transform: scaleY(1.2);
  }
}

// Container
.container {
  position: relative;
  z-index: 2;
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

// CTA Content
.ctaContent {
  max-width: 90vw;
  margin: 0 auto;
}

.badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(59, 130, 246, 0.15);
  border: 1px solid rgba(59, 130, 246, 0.4);
  border-radius: 50px;
  padding: 0.5rem 1.25rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(15px);
  overflow: hidden;
}

.badgeText {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badgeGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.2) 50%,
    transparent 100%
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.ctaTitle {
  font-size: 4rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1.5rem 0;
  line-height: 1.1;
  text-shadow: 0 0 40px rgba(59, 130, 246, 0.4);
}

.gradientText {
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
}

.ctaDescription {
  font-size: 1.15rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 3rem 0;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 3rem;
}

// CTA Buttons
.ctaButtons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

.primaryButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 0.75rem 2.25rem;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;

  &:hover {
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.2), 0 0 30px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    .buttonGlow {
      opacity: 1;
      transform: scale(1.2);
    }

    .buttonIcon {
      transform: translateX(4px);
    }
  }
}

.tertiaryButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-weight: 600;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid rgba(255, 255, 255, 0.1);

  &:hover {
    transform: translateY(-2px);
    color: rgba(255, 255, 255, 1);
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.05);

    .buttonIcon {
      transform: translateX(3px);
    }
  }
}

.buttonText {
  position: relative;
  z-index: 2;
}

.buttonGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.15) 0%,
    transparent 70%
  );
  border-radius: 50px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s ease;
}

.buttonIcon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

// Trust Indicators
.trustIndicators {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  margin-top: 2rem;
}

.trustItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;

  &:hover {
    opacity: 1;
  }
}

.trustIcon {
  width: 24px;
  height: 24px;
  color: rgba(59, 130, 246, 0.7);

  svg {
    width: 100%;
    height: 100%;
  }
}

.trustText {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

// Floating Animation
.floatingElement {
  animation: float 12s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(2deg);
  }
  50% {
    transform: translateY(-10px) rotate(-2deg);
  }
  75% {
    transform: translateY(-25px) rotate(1deg);
  }
}

// Pulsing Animation
.pulsingGlow {
  animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .container {
    max-width: 95vw;
    padding: 0 1.5rem;
  }

  .ctaTitle {
    font-size: 3.5rem;
  }

  .ctaDescription {
    font-size: 1.2rem;
  }

  .ctaButtons {
    gap: 1.25rem;
  }

  .trustIndicators {
    gap: 2rem;
  }

  .techGrid {
    width: 200px;
    height: 200px;
  }

  .glowOrb1 {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 968px) {
  .ctaBannerSection {
    padding: 6rem 0;
  }

  .ctaTitle {
    font-size: 3rem;
  }

  .ctaDescription {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
  }

  .ctaButtons {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 3rem;
  }

  .primaryButton,
  .secondaryButton,
  .tertiaryButton {
    width: 100%;
    max-width: 350px;
    justify-content: center;
  }

  .trustIndicators {
    flex-direction: column;
    gap: 1.5rem;
  }

  // Reduce background elements
  .hexagonPattern,
  .dataStream2 {
    display: none;
  }

  .circuitLines {
    width: 150px;
  }
}

@media (max-width: 768px) {
  .ctaBannerSection {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .ctaTitle {
    font-size: 2.5rem;
  }

  .ctaDescription {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .primaryButton,
  .secondaryButton,
  .tertiaryButton {
    font-size: 1rem;
    padding: 1rem 2.5rem;
  }

  .trustItem {
    gap: 0.5rem;
  }

  .trustIcon {
    width: 20px;
    height: 20px;
  }

  .trustText {
    font-size: 0.85rem;
  }

  // Further reduce background elements
  .glowOrb2,
  .glowOrb3,
  .dataStream1 {
    display: none;
  }

  .techGrid {
    width: 150px;
    height: 150px;
    opacity: 0.2;
  }

  .glowOrb1 {
    width: 100px;
    height: 100px;
    opacity: 0.5;
  }
}

@media (max-width: 480px) {
  .ctaBannerSection {
    padding: 4rem 0;
  }

  .ctaTitle {
    font-size: 2rem;
  }

  .ctaDescription {
    font-size: 0.95rem;
  }

  .primaryButton,
  .secondaryButton,
  .tertiaryButton {
    font-size: 0.95rem;
    padding: 0.875rem 2rem;
  }

  .trustIndicators {
    gap: 1rem;
  }

  .trustText {
    font-size: 0.8rem;
  }

  // Minimal background for very small screens
  .backgroundElements {
    opacity: 0.3;
  }
}
