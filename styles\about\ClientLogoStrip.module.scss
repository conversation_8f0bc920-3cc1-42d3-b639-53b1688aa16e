// Client Logo Strip Section
.logoStripSection {
  position: relative;
  padding: 6rem 0;
  background: 
    radial-gradient(ellipse 800px 400px at center top, rgba(0, 168, 204, 0.02) 0%, rgba(0, 168, 204, 0.005) 40%, transparent 70%),
    radial-gradient(ellipse 600px 300px at center bottom, rgba(0, 119, 190, 0.015) 0%, rgba(0, 119, 190, 0.003) 40%, transparent 70%),
    linear-gradient(135deg, #010204 0%, #020304 25%, #040507 50%, #020304 75%, #020306 100%);
  overflow: hidden;
}

// Container
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  position: relative;
}

// Section Header
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #00a8cc 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

// Logo Strip Container
.logoStrip {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 2rem 0;
  mask: linear-gradient(90deg, transparent 0%, black 10%, black 90%, transparent 100%);
  -webkit-mask: linear-gradient(90deg, transparent 0%, black 10%, black 90%, transparent 100%);
}

.logoContainer {
  display: flex;
  align-items: center;
  gap: 4rem;
  width: fit-content;
}

// Individual Logo Item
.logoItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  min-width: 120px;
  transition: all 0.4s ease;
  
  &:hover {
    transform: translateY(-5px);
    
    .logoPlaceholder {
      border-color: rgba(0, 168, 204, 0.4);
      background: rgba(0, 168, 204, 0.08);
    }
    
    .logoGlow {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.2);
    }
    
    .clientName {
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

// Logo Placeholder (for demonstration)
.logoPlaceholder {
  position: relative;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
}

.logoText {
  font-size: 1.2rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
}

.logoGlow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(0, 168, 204, 0.15) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.4s ease;
  z-index: -1;
}

.clientName {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  transition: all 0.4s ease;
  white-space: nowrap;
}

// Gradient Overlays for fade effect
.gradientOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 2;
}

.gradientLeft {
  position: absolute;
  top: 0;
  left: 0;
  width: 150px;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(1, 2, 4, 1) 0%, 
    rgba(1, 2, 4, 0.8) 30%, 
    rgba(1, 2, 4, 0.4) 60%, 
    transparent 100%
  );
}

.gradientRight {
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 100%;
  background: linear-gradient(270deg, 
    rgba(1, 2, 4, 1) 0%, 
    rgba(1, 2, 4, 0.8) 30%, 
    rgba(1, 2, 4, 0.4) 60%, 
    transparent 100%
  );
}

// Responsive Design
@media (max-width: 1200px) {
  .logoStripSection {
    padding: 5rem 0;
  }
  
  .sectionTitle {
    font-size: 2.25rem;
  }
  
  .logoContainer {
    gap: 3rem;
  }
  
  .logoItem {
    min-width: 100px;
  }
  
  .logoPlaceholder {
    width: 70px;
    height: 70px;
  }
  
  .logoText {
    font-size: 1rem;
  }
}

@media (max-width: 968px) {
  .logoStripSection {
    padding: 4rem 0;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .sectionHeader {
    margin-bottom: 3rem;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .sectionSubtitle {
    font-size: 1rem;
  }
  
  .logoContainer {
    gap: 2.5rem;
  }
  
  .logoItem {
    min-width: 90px;
  }
  
  .logoPlaceholder {
    width: 60px;
    height: 60px;
  }
  
  .logoText {
    font-size: 0.9rem;
  }
  
  .clientName {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .logoStripSection {
    padding: 3rem 0;
  }
  
  .sectionTitle {
    font-size: 1.75rem;
  }
  
  .logoContainer {
    gap: 2rem;
  }
  
  .logoItem {
    min-width: 80px;
  }
  
  .logoPlaceholder {
    width: 50px;
    height: 50px;
  }
  
  .logoText {
    font-size: 0.8rem;
  }
  
  .gradientLeft,
  .gradientRight {
    width: 100px;
  }
}

@media (max-width: 480px) {
  .logoStripSection {
    padding: 2rem 0;
  }
  
  .sectionHeader {
    margin-bottom: 2rem;
  }
  
  .sectionTitle {
    font-size: 1.5rem;
  }
  
  .logoContainer {
    gap: 1.5rem;
  }
  
  .logoItem {
    min-width: 70px;
  }
  
  .logoPlaceholder {
    width: 45px;
    height: 45px;
  }
  
  .logoText {
    font-size: 0.7rem;
  }
  
  .clientName {
    font-size: 0.7rem;
  }
  
  .gradientLeft,
  .gradientRight {
    width: 80px;
  }
}
