// Contact Map Section
.contactMapSection {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #020204 50%, #03050a 100%);
  overflow: hidden;
}

// Container
.container {
  position: relative;
  z-index: 2;
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
}

// Section Header
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1rem 0;
  line-height: 1.1;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.gradientText {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 1) 0%, 
    rgba(16, 185, 129, 1) 50%, 
    rgba(99, 102, 241, 1) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sectionSubtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

// Content Grid
.contentGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
  max-width: 1400px;
  margin: 0 auto;
}

// Map Container
.mapContainer {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  text-align: center;
  height: fit-content;
}

.mapPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.mapIcon {
  width: 80px;
  height: 80px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  
  svg {
    width: 40px;
    height: 40px;
    color: rgba(59, 130, 246, 0.8);
  }
}

.mapTitle {
  font-size: 2rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
}

.mapDescription {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.6;
  max-width: 400px;
}

.mapFeatures {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
  width: 100%;
}

.mapFeature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  
  svg {
    width: 20px;
    height: 20px;
    color: rgba(16, 185, 129, 0.8);
    flex-shrink: 0;
  }
  
  span {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(16, 185, 129, 0.2);
    transform: translateX(5px);
  }
}

// Office Info
.officeInfo {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.officeCard {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-5px);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 
      0 15px 35px rgba(0, 0, 0, 0.2),
      0 0 20px rgba(59, 130, 246, 0.15);
  }
  
  &.primary {
    border-color: rgba(59, 130, 246, 0.2);
    
    .officeIcon {
      background: rgba(59, 130, 246, 0.1);
      border-color: rgba(59, 130, 246, 0.2);
      
      svg {
        color: rgba(59, 130, 246, 0.8);
      }
    }
  }
  
  &.secondary {
    border-color: rgba(16, 185, 129, 0.2);
    
    .officeIcon {
      background: rgba(16, 185, 129, 0.1);
      border-color: rgba(16, 185, 129, 0.2);
      
      svg {
        color: rgba(16, 185, 129, 0.8);
      }
    }
  }
  
  &.support {
    border-color: rgba(99, 102, 241, 0.2);
    
    .officeIcon {
      background: rgba(99, 102, 241, 0.1);
      border-color: rgba(99, 102, 241, 0.2);
      
      svg {
        color: rgba(99, 102, 241, 0.8);
      }
    }
  }
}

.officeHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.officeIcon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  svg {
    width: 24px;
    height: 24px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.officeName {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
}

.officeDetails {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.officeDetail {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.detailIcon {
  width: 20px;
  height: 20px;
  color: rgba(255, 255, 255, 0.6);
  flex-shrink: 0;
  margin-top: 0.125rem;
  
  svg {
    width: 100%;
    height: 100%;
  }
}

.detailText {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.officeActions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.actionButton {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  
  svg {
    width: 16px;
    height: 16px;
  }
  
  &:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
    color: rgba(59, 130, 246, 0.9);
    transform: translateY(-2px);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .container {
    max-width: 95vw;
    padding: 0 1.5rem;
  }

  .contentGrid {
    gap: 3rem;
  }

  .sectionTitle {
    font-size: 3rem;
  }

  .mapContainer {
    padding: 2.5rem;
  }

  .officeCard {
    padding: 1.75rem;
  }
}

@media (max-width: 968px) {
  .contactMapSection {
    padding: 6rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .sectionSubtitle {
    font-size: 1.1rem;
  }

  .contentGrid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .mapContainer {
    padding: 2rem;
    order: 2;
  }

  .officeInfo {
    order: 1;
  }

  .mapTitle {
    font-size: 1.75rem;
  }

  .mapDescription {
    font-size: 1rem;
  }

  .mapFeatures {
    margin-top: 1.5rem;
  }

  .officeName {
    font-size: 1.25rem;
  }
}

@media (max-width: 768px) {
  .contactMapSection {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }

  .mapContainer {
    padding: 1.5rem;
  }

  .mapIcon {
    width: 60px;
    height: 60px;

    svg {
      width: 30px;
      height: 30px;
    }
  }

  .mapTitle {
    font-size: 1.5rem;
  }

  .mapDescription {
    font-size: 0.95rem;
  }

  .mapFeature {
    padding: 0.875rem;

    svg {
      width: 18px;
      height: 18px;
    }

    span {
      font-size: 0.9rem;
    }
  }

  .officeCard {
    padding: 1.5rem;
  }

  .officeIcon {
    width: 44px;
    height: 44px;

    svg {
      width: 22px;
      height: 22px;
    }
  }

  .officeName {
    font-size: 1.1rem;
  }

  .detailText {
    font-size: 0.9rem;
  }

  .actionButton {
    padding: 0.625rem 1.25rem;
    font-size: 0.85rem;

    svg {
      width: 14px;
      height: 14px;
    }
  }
}

@media (max-width: 480px) {
  .contactMapSection {
    padding: 4rem 0;
  }

  .sectionTitle {
    font-size: 1.75rem;
  }

  .sectionSubtitle {
    font-size: 0.95rem;
  }

  .mapContainer {
    padding: 1.25rem;
  }

  .mapIcon {
    width: 50px;
    height: 50px;
    margin-bottom: 0.75rem;

    svg {
      width: 25px;
      height: 25px;
    }
  }

  .mapTitle {
    font-size: 1.25rem;
  }

  .mapDescription {
    font-size: 0.9rem;
  }

  .mapFeatures {
    gap: 0.75rem;
    margin-top: 1.25rem;
  }

  .mapFeature {
    padding: 0.75rem;
    gap: 0.5rem;

    svg {
      width: 16px;
      height: 16px;
    }

    span {
      font-size: 0.85rem;
    }
  }

  .officeInfo {
    gap: 1.5rem;
  }

  .officeCard {
    padding: 1.25rem;
  }

  .officeHeader {
    gap: 0.75rem;
    margin-bottom: 1.25rem;
  }

  .officeIcon {
    width: 40px;
    height: 40px;

    svg {
      width: 20px;
      height: 20px;
    }
  }

  .officeName {
    font-size: 1rem;
  }

  .officeDetails {
    gap: 0.875rem;
    margin-bottom: 1.5rem;
  }

  .officeDetail {
    gap: 0.5rem;
  }

  .detailIcon {
    width: 18px;
    height: 18px;
  }

  .detailText {
    font-size: 0.85rem;
  }

  .officeActions {
    gap: 0.75rem;
  }

  .actionButton {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;

    svg {
      width: 12px;
      height: 12px;
    }
  }
}
