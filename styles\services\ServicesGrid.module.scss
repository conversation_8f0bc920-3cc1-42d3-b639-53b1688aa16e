.visualSection {
  display: flex;
  justify-content: center;
  align-items: center;
}

.servicesGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  max-width: 500px;
}

.serviceCard {
  padding: 2rem 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;

  &.clickable {
    cursor: pointer;

    &:active {
      transform: translateY(-6px) scale(0.98);
    }
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 168, 204, 0.1), transparent);
    transition: left 0.6s ease;
  }
  
  &:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 168, 204, 0.4);
    box-shadow: 
      0 20px 60px rgba(0, 168, 204, 0.15),
      0 0 0 1px rgba(0, 168, 204, 0.1);
    
    &::before {
      left: 100%;
    }
    
    .serviceIcon {
      transform: scale(1.1) rotate(5deg);
    }
    
    .serviceTitle {
      color: rgba(0, 168, 204, 0.9);
    }
  }
}

.serviceIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 4px 8px rgba(0, 168, 204, 0.2));
}

.serviceTitle {
  font-family: 'satoshi', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--white);
  margin: 0 0 0.5rem 0;
  transition: color 0.3s ease;
  position: relative;
}

.serviceDesc {
  font-family: 'manrope', sans-serif;
  font-size: 0.9rem;
  color: var(--parawhite);
  margin: 0;
  line-height: 1.4;
}

// Responsive Design
@media (max-width: 968px) {
  .servicesGrid {
    max-width: 600px;
  }
}

@media (max-width: 768px) {
  .servicesGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 400px;
  }
  
  .serviceCard {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .serviceCard {
    padding: 1.25rem 0.875rem;
  }
  
  .serviceIcon {
    font-size: 2rem;
  }
  
  .serviceTitle {
    font-size: 1rem;
  }
  
  .serviceDesc {
    font-size: 0.85rem;
  }
}
