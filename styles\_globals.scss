@use "tailwindcss";
@use "performance-optimizations";

// @import "tw-animate-css";

// @custom-variant dark (&:is(.dark *));

// Font declarations now handled by next/font/local in layout.jsx
// Using CSS variables for better performance and consistency

:root {
  --head: rgb(25, 32, 36);
  --parawhite: rgb(255, 255, 255, 0.9);
  --para: rgb(0, 0, 0, 0.7);
  --white: #ffffff;
  --black: #050505;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

body {
  background: var(--black);
  color: var(--white);
  font-family: "satoshi", sans-serif;
  font-optical-sizing: auto;
}

.mainPage {
  max-width: 99.5vw !important;
  overflow-x: hidden;
}

.testing {
  border: 5px solid red;
}

.white {
  color: var(--white);
}

.bgBlack {
  background-color: var(--black) !important;
}

::-webkit-scrollbar {
  width: 5px;
  border-radius: 5rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: var(--lightblue);
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #b5b4b4;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #888;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

input:focus,
textarea:focus {
  outline: none;
}

.carouselbtn,
button,
img,
svg {
  cursor: pointer !important;
}

span {
  color: var(--white);
}

h1,
h2,
h3 {
  color: var(--white);
  font-family: "satoshi", sans-serif;
}

span,
a {
  font-family: "satoshi", sans-serif;
}

p {
  color: var(--parawhite);
  font-family: "manrope", sans-serif;
  text-align: center;
}

.clash {
  font-family: "clash", sans-serif;
  font-weight: 900 !important;
}

// img,
// svg {
//   transition: 0.2s all;
// }

// img:hover,
// svg:hover {
//   transform: scale(1.05);
// }

.whitebgButton {
  padding: 0.5rem 4rem;
  background-color: var(--white);
  align-self: center;
  cursor: pointer;
  z-index: 9;
}

.blackBtn {
  padding: 0.5rem 4rem;
  background-color: var(--black);
  color: var(--white);
  text-align: center;
  font-size: 0.9rem;
  align-self: center;
  cursor: pointer;
  z-index: 9;
  border-radius: 0.2rem;
}

.imgContainer {
  overflow: hidden;
}

.imgContainerr {
  width: 100%;
  height: 24rem;
  position: relative;
  overflow: hidden;

  & > div {
    position: absolute;
    z-index: 9;
    font-size: 0.9rem;
    background-color: var(--white);
    top: 1rem;
    left: 1rem;
    color: rgba(208, 2, 27, 1);
    padding: 0.25rem 0.5rem;
  }
}

.textGradient {
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
  display: inline-block;
}

.lampBg {
  background-color: #050505;
}

.gridContainer {
  .bentoCard {
    .bentoImgContainer {
      img {
        height: 100%;
      }
    }

    h3 {
      font-size: 1.5rem;
    }

    p {
      font-size: 1.1rem;
      text-align: start;
    }
  }
}

// checkout cart styling
.cartContainer {
  margin-top: 5rem;
  padding: 2rem;
  background-color: var(--white);
  min-width: 30vw !important;
  max-width: 30vw !important;

  .title {
    font-size: 1.5rem;
  }
  .cartItems {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    & > div {
      display: grid;
      grid-template-columns: 25% 70%;
      gap: 1rem;
      .imgContainer {
        position: relative;
        width: 100%;
        height: 8rem;
      }

      .info {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        & > div {
          display: flex;
          justify-content: space-between;
          align-items: center;

          h3 {
            font-size: 1.1rem;
          }

          .quantityContainer {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid rgba(221, 219, 220, 1);
            padding: 0.2rem 1rem;
          }

          span {
            font-size: 0.9rem;
          }
        }

        svg {
          transform: scale(1.2);
        }
      }
    }
  }

  .cartCheckOutContainer {
    position: fixed;
    bottom: 4rem;
    right: 0;
    width: 30vw;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    box-shadow: 0px -4px 8px rgba(0, 0, 0, 0.08);
    background-color: var(--white);

    & > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      p {
        color: var(--black);
        font-weight: 600;
      }

      button {
        width: 100%;
      }
    }

    span {
      font-size: 0.9rem;
    }
  }
}

// custom animations since tailwind css isnt working
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in {
  animation: slideInRight 0.5s ease-in-out forwards;
}

.animate-slide-out {
  animation: slideOutRight 0.5s ease-in-out forwards;
}

// Loader animation for Spline component
.loader {
  width: 48px;
  height: 48px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Testimonials scroll animation
@keyframes scroll-up {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

// Infinite slider animations
@keyframes slide-horizontal {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes slide-vertical {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

//title
@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
