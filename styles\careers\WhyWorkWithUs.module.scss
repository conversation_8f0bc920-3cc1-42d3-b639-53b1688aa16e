// Why Work With Us Section
.whyWorkSection {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(135deg, #0a0a0a 0%, #020204 50%, #03050a 100%);
  overflow: hidden;
}

// Background Elements
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floatingShape1 {
  position: absolute;
  top: 10%;
  left: 5%;
  width: 120px;
  height: 120px;
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.08) 0%,
    rgba(16, 185, 129, 0.06) 100%
  );
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  filter: blur(1px);
  opacity: 0.6;
}

.floatingShape2 {
  position: absolute;
  top: 60%;
  right: 8%;
  width: 80px;
  height: 80px;
  background: linear-gradient(
    45deg,
    rgba(99, 102, 241, 0.06) 0%,
    rgba(59, 130, 246, 0.08) 100%
  );
  border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  filter: blur(1px);
  opacity: 0.5;
}

.floatingShape3 {
  position: absolute;
  bottom: 15%;
  left: 15%;
  width: 100px;
  height: 100px;
  background: linear-gradient(
    45deg,
    rgba(16, 185, 129, 0.05) 0%,
    rgba(99, 102, 241, 0.07) 100%
  );
  border-radius: 40% 60% 60% 40% / 40% 40% 60% 60%;
  filter: blur(1px);
  opacity: 0.4;
}

.glowingOrb1 {
  position: absolute;
  top: 25%;
  right: 20%;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(59, 130, 246, 0.05) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

.glowingOrb2 {
  position: absolute;
  bottom: 30%;
  left: 25%;
  width: 100px;
  height: 100px;
  background: radial-gradient(
    circle,
    rgba(16, 185, 129, 0.08) 0%,
    rgba(16, 185, 129, 0.04) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(2px);
}

// Container
.container {
  position: relative;
  z-index: 2;
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
}

// Section Header
.sectionHeader {
  text-align: center;
  margin-bottom: 4rem;
}

.sectionTitle {
  font-size: 3.5rem;
  font-weight: 800;
  color: #ffffff;
  margin: 0 0 1rem 0;
  line-height: 1.1;
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
}

.sectionSubtitle {
  font-size: 1.15rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.6;
  max-width: 90%;
  margin: 0 auto;
}

// Cards Grid
.cardsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
  gap: 2rem;
  max-width: 90vw;
  margin: 0 auto;
}

// Card Styles
.card {
  position: relative;
  padding: 2rem 1rem;
  border-radius: 24px;
  backdrop-filter: blur(15px);
  transition: all 0.4s ease;
  overflow: hidden;
  cursor: pointer;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 168, 204, 0.4) 50%,
      transparent 100%
    );
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  &:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(0, 168, 204, 0.2);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5), 0 0 40px rgba(0, 168, 204, 0.08);

    &::before {
      opacity: 1;
    }

    .cardGlow {
      opacity: 1;
      transform: scale(1.1);
    }

    .icon {
      transform: scale(1.2) rotate(10deg);
    }
  }
}

.cardIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  margin: 0 0 1.5rem 0;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 50%;
  transition: all 0.4s ease;
}

.iconContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.icon {
  width: 30px;
  height: 30px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.4s ease;
  filter: drop-shadow(0 0 10px rgba(0, 168, 204, 0.3));
}

.cardContent {
  position: relative;
  z-index: 2;
}

.cardTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0 0 1rem 0;
  line-height: 1.3;
  transition: all 0.3s ease;
}

.cardDescription {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.6;
  text-align: start;
}

.cardGlow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(59, 130, 246, 0.05) 0%,
    transparent 70%
  );
  border-radius: 20px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.4s ease;
  pointer-events: none;
}

// Floating Animation
.floatingElement {
  animation: float 8s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(1deg);
  }
  50% {
    transform: translateY(-5px) rotate(-1deg);
  }
  75% {
    transform: translateY(-15px) rotate(0.5deg);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .container {
    max-width: 95vw;
    padding: 0 1.5rem;
  }

  .sectionTitle {
    font-size: 3rem;
  }

  .cardsGrid {
    gap: 1.5rem;
  }

  .card {
    padding: 2rem 1.5rem;
  }

  .floatingShape1 {
    width: 100px;
    height: 100px;
  }

  .glowingOrb1 {
    width: 120px;
    height: 120px;
  }
}

@media (max-width: 968px) {
  .whyWorkSection {
    padding: 6rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .sectionTitle {
    font-size: 2.5rem;
  }

  .sectionSubtitle {
    font-size: 1.1rem;
  }

  .cardsGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .card {
    padding: 2rem 1.5rem;

    &:hover {
      transform: translateY(-5px) scale(1.01);
    }
  }

  .cardIcon {
    width: 70px;
    height: 70px;
    margin-bottom: 1.25rem;
  }

  .icon {
    width: 35px;
    height: 35px;
  }

  .cardTitle {
    font-size: 1.3rem;
  }

  .floatingShape2,
  .floatingShape3 {
    display: none;
  }
}

@media (max-width: 768px) {
  .whyWorkSection {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionSubtitle {
    font-size: 1rem;
  }

  .cardsGrid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .card {
    padding: 1.75rem 1.25rem;
  }

  .cardIcon {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }

  .icon {
    width: 30px;
    height: 30px;
  }

  .cardTitle {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .cardDescription {
    font-size: 0.95rem;
  }

  // Reduce background elements for mobile
  .glowingOrb2,
  .floatingShape1 {
    display: none;
  }

  .glowingOrb1 {
    width: 80px;
    height: 80px;
    opacity: 0.3;
  }
}

@media (max-width: 480px) {
  .whyWorkSection {
    padding: 4rem 0;
  }

  .sectionHeader {
    margin-bottom: 2.5rem;
  }

  .sectionTitle {
    font-size: 1.75rem;
  }

  .sectionSubtitle {
    font-size: 0.95rem;
  }

  .card {
    padding: 1.5rem 1rem;
  }

  .cardIcon {
    width: 50px;
    height: 50px;
  }

  .icon {
    width: 25px;
    height: 25px;
  }

  .cardTitle {
    font-size: 1.1rem;
  }

  .cardDescription {
    font-size: 0.9rem;
  }

  // Minimal background for very small screens
  .backgroundElements {
    opacity: 0.5;
  }
}
