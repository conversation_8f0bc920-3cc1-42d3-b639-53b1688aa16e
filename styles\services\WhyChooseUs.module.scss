.whyChooseUs {
  position: relative;
  padding: 8rem 0;
  background: linear-gradient(
    180deg,
    rgba(2, 2, 4, 0.9) 0%,
    rgba(3, 5, 10, 0.8) 100%
  );
  overflow: hidden;
}

.container {
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 10;
}

// Section Header
.sectionHeader {
  text-align: center;
  margin-bottom: 1rem;
}

.sectionTitle {
  font-family: "satoshi", sans-serif;
  font-size: 3rem;
  font-weight: 700;
  color: var(--white);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.gradientText {
  background: linear-gradient(135deg, #00a8cc, #0077be, #004d7a);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
  filter: drop-shadow(0 0 20px rgba(0, 168, 204, 0.3));
}

.sectionDescription {
  font-family: "manrope", sans-serif;
  font-size: 1.1rem;
  line-height: 1.6;
  letter-spacing: 0.05rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 90%;
  margin: 0 auto;
}


.featureCards{
  padding: 1rem 0;

  h2{
    font-size: 1.25rem;
    font-weight: 600;
  }
  p{
    text-align: start;
    margin-right: 1rem;
  }
}


// Background Elements
.backgroundElements {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 1;
}

.floatingOrb1 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(0, 168, 204, 0.1) 0%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(40px);
  animation: float 15s ease-in-out infinite;
}

.floatingOrb2 {
  position: absolute;
  bottom: 25%;
  right: 12%;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle,
    rgba(0, 119, 190, 0.08) 0%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(30px);
  animation: float 18s ease-in-out infinite reverse;
}

.floatingOrb3 {
  position: absolute;
  top: 60%;
  left: 15%;
  width: 120px;
  height: 120px;
  background: radial-gradient(
    circle,
    rgba(0, 168, 204, 0.06) 0%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(25px);
  animation: float 12s ease-in-out infinite;
}

.gridPattern {
  position: absolute;
  inset: 0;
  background-image: linear-gradient(
      rgba(0, 168, 204, 0.02) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(0, 168, 204, 0.02) 1px, transparent 1px);
  background-size: 60px 60px;
  opacity: 0.4;
}

// Animations
@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-20px) translateX(15px);
  }
  66% {
    transform: translateY(15px) translateX(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.1);
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .sectionTitle {
    font-size: 2.5rem;
  }

  .advantagesGrid {
    gap: 1.5rem;
  }

  .advantageCard {
    padding: 2rem;

    // Adjust card layouts for tablet
    &.trust,
    &.collaboration,
    &.speed {
      .statsGrid {
        grid-template-columns: repeat(2, 1fr);
      }

      .highlightsList {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    &.growth {
      .statsGrid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

@media (max-width: 968px) {
  .container {
    padding: 0 1rem;
  }

  .whyChooseUs {
    padding: 6rem 0;
  }

  .sectionTitle {
    font-size: 2.25rem;
  }

  .sectionDescription {
    font-size: 1.1rem;
  }

  .advantagesGrid {
    gap: 2rem;
  }

  .advantageCard {
    // Reset all card layouts for mobile
    &.trust,
    &.growth,
    &.collaboration,
    &.speed {
      text-align: center;

      .cardHeader {
        flex-direction: column;
        text-align: center;
        align-items: center;
        justify-content: center;
      }

      .advantageIcon {
        font-size: 3.5rem;
        margin: 0 0 1rem 0;
      }

      .statsGrid {
        grid-template-columns: repeat(2, 1fr);
        max-width: none;
        margin-bottom: 2rem;
      }

      .highlightsList {
        grid-template-columns: 1fr;
        max-width: none;
        margin: 0;
      }
    }
  }

  .advantageCard {
    padding: 2rem 1.5rem;
  }

  .cardHeader {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .advantageIcon {
    font-size: 3.5rem;
  }

  .advantageTitle {
    font-size: 1.5rem;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .highlightsList {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .ctaButtons {
    flex-direction: column;
    gap: 1rem;

    button {
      width: 100%;
      max-width: 300px;
    }
  }

  .trustIndicators {
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .whyChooseUs {
    padding: 4rem 0;
  }

  .sectionHeader {
    margin-bottom: 3rem;
  }

  .sectionTitle {
    font-size: 2rem;
  }

  .sectionDescription {
    font-size: 1rem;
  }

  .advantageCard {
    padding: 1.5rem;
  }

  .advantageIcon {
    font-size: 3rem;
  }

  .advantageTitle {
    font-size: 1.25rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .statItem {
    padding: 0.75rem;
  }

  .statValue {
    font-size: 1.5rem;
  }

  .ctaSection {
    padding: 2rem 1rem;
  }

  .ctaTitle {
    font-size: 1.75rem;
  }

  .ctaDescription {
    font-size: 0.95rem;
  }

  .primaryCta,
  .secondaryCta {
    padding: 0.875rem 1.75rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .whyChooseUs {
    padding: 3rem 0;
  }

  .sectionTitle {
    font-size: 1.75rem;
  }

  .advantageCard {
    padding: 1.25rem;
  }

  .advantageIcon {
    font-size: 2.5rem;
  }

  .advantageTitle {
    font-size: 1.1rem;
  }

  .advantageDescription {
    font-size: 0.9rem;
  }

  .statValue {
    font-size: 1.25rem;
  }

  .statLabel {
    font-size: 0.8rem;
  }

  .highlightItem {
    font-size: 0.85rem;
  }

  .ctaTitle {
    font-size: 1.5rem;
  }

  .ctaDescription {
    font-size: 0.9rem;
  }

  .primaryCta,
  .secondaryCta {
    padding: 0.75rem 1.5rem;
    font-size: 0.85rem;
  }

  .trustItem {
    gap: 0.75rem;
  }

  .trustIcon {
    font-size: 1.5rem;
  }

  .trustTitle {
    font-size: 0.85rem;
  }

  .trustDesc {
    font-size: 0.75rem;
  }
}
